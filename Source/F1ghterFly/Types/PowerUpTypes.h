#pragma once

#include "CoreMinimal.h"
#include <NiagaraSystem.h>
#include "PowerUpTypes.generated.h"


// Forward declaration to avoid circular dependency
class ABaseCollectible;

// the amount of power up slots a player can have
static const int16 Power_Up_Slot_Limit = 3;

UENUM(BlueprintType)
enum class ECollectibleType : uint8
{
	None,
	Coin, // maybe "Aether Shards"
	PowerUp,
	MAX UMETA(Hidden)
};


UENUM(BlueprintType)
enum class EPowerUpType : uint8
{
    None,
    TurboBoost,
    EnergyShield,
    MagnetField,
    DoubleDash,
    RocketBarrage,
    ScoreMultiplier,
    Health,
    MAX UMETA(Hidden)
};

// Bitwise enum for Jet effect modes (up to 32 modes with uint32)
UENUM(BlueprintType, Meta = (Bitflags, UseEnumValuesAsMaskValuesInEditor = "true"))
enum class EJetEffectMode : uint8
{
	None = 0,
	Boosting = 1 << 0, // TurboBoost (double speed)
	Magnetizing = 1 << 1, // Magnet (pull items)
	ScoreMultiplying = 1 << 2, // ScoreMultiplier (double points)
	Slowing = 1 << 3, // SlowDown (half speed)
	Shielding = 1 << 4, // Shield (no damage)
	Freezing = 1 << 5, // Freeze (freeze self or others)
	DoubleDashing = 1 << 6, // DoubleDash (double dash ability)
	MissileStorming = 1 << 7  // MissileStorm (rapid missile barrage) i.e Rocket Barrage
};
ENUM_CLASS_FLAGS(EJetEffectMode)


USTRUCT(BlueprintType)
struct FPowerUpSpawnChances
{
	GENERATED_BODY()

	// Unique identifier for the power-up type (e.g., "TurboBoost", "EnergyShield")
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PowerUp")
	EPowerUpType PowerUpType;

	// Base chance of spawning this power-up
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PowerUp", meta = (ClampMin = "0.0", ClampMax = "100.0"))
	float BaseChance;

	FPowerUpSpawnChances()
		: PowerUpType(EPowerUpType::None)
		, BaseChance(0.0f)
	{}
};

USTRUCT(BlueprintType)
struct FPowerUpPickUp
{
	GENERATED_BODY()

	// Unique identifier for the power-up type (e.g., "TurboBoost", "EnergyShield")
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PowerUp")
	EPowerUpType PowerUpType;

	// Duration of the power-up effect in seconds
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PowerUp", meta = (ClampMin = "0.0"))
	float Duration;

	/// <summary>
	///  Slot number for the power-up. This is used to identify which slot the power-up is assigned to.
	/// </summary>
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "PowerUp")
	int32 SlotNumber;

	FPowerUpPickUp() : PowerUpType(EPowerUpType::None), Duration(0.0f), SlotNumber(0) {}
};

USTRUCT(BlueprintType)
struct FCollectibleAssetData : public FTableRowBase
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collectible")
	ECollectibleType CollectibleType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collectible")
	EPowerUpType PowerUpType;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Collectible")
    TSubclassOf<ABaseCollectible> CollectibleClasses;

	FCollectibleAssetData() : 
		CollectibleType(ECollectibleType::None),
		PowerUpType(EPowerUpType::None),
		CollectibleClasses(nullptr)
	{
	}
};

USTRUCT(BlueprintType)
struct FEffectData
{
	GENERATED_BODY()

	// Player that triggered the effect. This can be use ideas where we do not want to affect the player that trigger the effect.
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
	int32 OwnerID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
	int32 EffectID;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
	EPowerUpType EffectType;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effect")
	float Duration;

	UPROPERTY(BlueprintReadOnly, Category = "Effect")
	FTimerHandle TimerHandle; // Non-replicated, server-side

	FEffectData() :
		OwnerID(0),
		EffectID(0),
		EffectType(EPowerUpType::None),
		Duration(0.0f)
	{}
};

USTRUCT(BlueprintType)
struct FRequestSlotActivation
{
	GENERATED_BODY()

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	int32 SlotNumber;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
	int32 JetID;

	FRequestSlotActivation() :
		SlotNumber(0),
		JetID(0)
	{
	}

	FRequestSlotActivation(int32 InSlotNumber, int32 InJetID) :
		SlotNumber(InSlotNumber),
		JetID(InJetID)
	{
	}
};

/**
 * Struct for Data Table rows defining static VFX configurations for jets.
 * Contains only static properties for VFX appearance and attachment.
 */
USTRUCT(BlueprintType)
struct FJetVfxAssetData : public FTableRowBase
{
	GENERATED_BODY()

public:
	// Unique identifier for the VFX type (e.g., "Magnet", "SpeedBoost")
	// Matches Data Table row name for lookups
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
	EPowerUpType EffectType;

	// Niagara system asset for the VFX (e.g., NS_MagnetPowerUp)
	// Soft reference to reduce memory usage
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
	TSoftObjectPtr<UNiagaraSystem> NiagaraSystem;

	// Socket on jet mesh to attach VFX (e.g., "MagnetSocket")
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "VFX")
	FName SocketName;

	// Default constructor
	FJetVfxAssetData()
		: EffectType(EPowerUpType::None)
		, NiagaraSystem(nullptr)
		, SocketName(NAME_None)
	{
	}
};

USTRUCT(BlueprintType)
struct FActiveVFX
{
	GENERATED_BODY()

	UPROPERTY()
	int32 EffectID;
	
	UPROPERTY()
	UNiagaraComponent* Component;

	FActiveVFX() :
		EffectID(0),
		Component(nullptr)
	{
	}

	FActiveVFX(int32 EffectID, UNiagaraComponent* Component) :
		EffectID(EffectID),
		Component(Component)
	{
	}
};