// Fill out your copyright notice in the Description page of Project Settings.


#include "DamageDealer.h"
#include <F1ghterFlyGameState.h>
#include "Projectile.h"
#include "ActorComponents/ScoreManager.h"
#include "Interfaces/IDamageable.h"
#include "F1ghterFlyPlayerState.h"

// Sets default values
ADamageDealer::ADamageDealer()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
    //PrimaryActorTick.bCanEverTick = true;

    // Create Anchor
    USceneComponent* Anchor = CreateDefaultSubobject<USceneComponent>(TEXT("Anchor"));
    RootComponent = Anchor;

    DamageCollider = CreateDefaultSubobject<UBoxComponent>(TEXT("DamageCollider"));
    DamageCollider->SetBoxExtent(FVector(20.f, 20.f, 20.f)); // Default size, adjustable in blueprints
    DamageCollider->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    // DamageCollider->SetupAttachment(Anchor);

    // Default damage to capsule default value if not provided
    WingDamageAmount = WingDamageAmount ? WingDamageAmount : CapsuleDamageAmount;
    DamageResidualEffectType = EDamageResidualEffectType::None;
}


// Post Initialize Components
void ADamageDealer::PostInitializeComponents()
{
    Super::PostInitializeComponents();

    InitializeDamageColliders();
}

// Called when the game starts or when spawned
void ADamageDealer::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void ADamageDealer::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void ADamageDealer::InitializeDamageColliders()
{
    // Get Damage Collider
    DamageCollider = FindComponentByClass<UBoxComponent>();
    
    if (DamageCollider)
    {
        DamageCollider->OnComponentBeginOverlap.AddDynamic(this, &ADamageDealer::OnDamageOverlap);
    }
}

void ADamageDealer::SetDamageOverlapEnabled(bool bEnabled)
{
    if (DamageCollider)
    {
        DamageCollider->SetGenerateOverlapEvents(bEnabled);
    }
}

void ADamageDealer::OnDamageOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
    if (AF1ghterFlyCharacter* Jet = Cast<AF1ghterFlyCharacter>(OtherActor))
    {
        if (OtherComp)
        {

            bool bIsWingHit = OtherComp->ComponentHasTag(FName("Wing"));
            EJetBodyPart BodyPart = bIsWingHit ? EJetBodyPart::Wings : EJetBodyPart::MainBody;

            // Calculate proximity using closest points on colliders
            FVector ClosestPointOnObstacle;
            FVector ClosestPointOnJet;
            OverlappedComponent->GetClosestPointOnCollision(Jet->GetActorLocation(), ClosestPointOnObstacle);
            OtherComp->GetClosestPointOnCollision(GetActorLocation(), ClosestPointOnJet);

            float RawDistance = FVector::Distance(ClosestPointOnObstacle, ClosestPointOnJet);

            // Define proximity threshold (e.g., 100 units)
            const float MaxProximityDistance = 100.0f;

            // Normalize proximity (0.0 = far, 1.0 = close)
            float Proximity = FMath::Clamp(1.0f - (RawDistance / MaxProximityDistance), 0.0f, 1.0f);
           // UE_LOG(LogTemp, Warning, TEXT("RAW DISTANCE: %f, NORMALIZED PROXIMITY: %f , body part: %s"), RawDistance, Proximity, bIsWingHit ? TEXT("WINGS") : TEXT("CENTER"));

            // If near miss, add points to hype meter
            if (Proximity < 0.5) {  
                
                // locally controlled client only logic.
                if(Jet->IsLocallyControlled()) {
                    // start timer for near miss. This is used to check if the player fully avoided the obstacle
                    StartNearMissTimer(Jet, Proximity);
                }
            }
            else {
                // locally controlled client only logic. Broadcast event to apply cosmetic effect such as camera shake. This is an optimistic approach.
                if (Jet->IsLocallyControlled()) {
                    Jet->OnJetImpact.Broadcast(BodyPart);

                    // clear pending new miss timer
                    GetWorld()->GetTimerManager().ClearTimer(NearMissTimerHandle);
                    PendingNearMissJet = nullptr;
                    PendingNearMissProximity = -1.0f;
                }

                // Apply damage
                ApplyDamage(Jet, BodyPart, GetDamageAmount(BodyPart));
                
            }
        }
    }
}

void ADamageDealer::ApplyDamage(AActor* TargetActor, const EJetBodyPart Part, const float Damage)
{
    if (!TargetActor || !CanApplyDamage(TargetActor))
    {
        return;
    }

    LastDamageTimes.Add(TargetActor, GetWorld()->GetTimeSeconds());

    // Check if it's a jet using tag (most common case)
    if (TargetActor->ActorHasTag("Jet"))
    {
        if (APawn* Pawn = Cast<APawn>(TargetActor))
        {
            if (AF1ghterFlyPlayerState* PlayerState = Cast<AF1ghterFlyPlayerState>(Pawn->GetPlayerState()))
            {
                float HypeDecay = Part == EJetBodyPart::Wings ? HypeMeterDecayAmount / 0.5f : HypeMeterDecayAmount;
                PlayerState->ApplyDamage(Damage, Part, HypeDecay);
                return;
            }
        }
    }

    // Fallback to IDamageable interface for other actors (turrets, etc.)
    if (TargetActor->Implements<UDamageable>())
    {
        if (IDamageable* Damageable = Cast<IDamageable>(TargetActor))
        {
            Damageable->DamageTaken(Damage);
        }
    }
}

float ADamageDealer::GetDamageAmount(EJetBodyPart Part) const
{
    switch (Part)
    {
        case EJetBodyPart::Wings:
            return WingDamageAmount;
        case EJetBodyPart::None:
            return CapsuleDamageAmount;
        case EJetBodyPart::LeftWing:
        case EJetBodyPart::RightWing:
        case EJetBodyPart::MainBody:
        case EJetBodyPart::Tail:
        default:
            return CapsuleDamageAmount;
    }
}

bool ADamageDealer::CanApplyDamage(AActor* TargetActor) const
{
    if (!TargetActor)
    {
        return false;
    }
    
    float CurrentTime = GetWorld()->GetTimeSeconds();
    if (const float* LastDamageTime = LastDamageTimes.Find(TargetActor))
    {
        return (CurrentTime - *LastDamageTime) >= DamageCooldown;
    }

    return true;
}

/**
 * Start a timer to check if the player fully avoided the obstacle.
 *  - After the delay, if the player did not hit the obstacle, send a server RPC to add score for near miss.
 */
void ADamageDealer::StartNearMissTimer(AF1ghterFlyCharacter* Jet, float Proximity)
{
    // Store the jet and proximity for the delayed check
    PendingNearMissJet = Jet;
    PendingNearMissProximity = Proximity;
    
    // Clear any existing timer
    GetWorld()->GetTimerManager().ClearTimer(NearMissTimerHandle);
    
    // Set a timer to check if the player fully avoided the obstacle
    GetWorld()->GetTimerManager().SetTimer(
        NearMissTimerHandle,
        this,
        &ADamageDealer::ProcessPendingNearMiss,
        0.5f, // Delay time to confirm avoidance (adjust as needed)
        false // Don't loop
    );
}


void ADamageDealer::ProcessPendingNearMiss()
{
    if (PendingNearMissJet && PendingNearMissProximity >= 0.0f)
    {
        // Add score for near miss
        PendingNearMissJet->GetScoreManager()->ProcessAndAddProximityScore(PendingNearMissProximity);
    }

    // Clear pending data
    PendingNearMissJet = nullptr;
    PendingNearMissProximity = -1.0f;
}
