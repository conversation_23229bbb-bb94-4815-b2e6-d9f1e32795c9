// Fill out your copyright notice in the Description page of Project Settings.


#include "EnvPlatform.h"
#include "Components/BoxComponent.h"
#include "F1ghterFly/Types/PlatformTypes.h"
#include "F1ghterFlyCharacter.h"
#include "GameModes/F1ghterFlyGameMode.h"
#include "SpawnManager.h"
#include <F1ghterFlyGameState.h>
#include <DamageDealer.h>

#include "Net/UnrealNetwork.h"

// Sets default values
AEnvPlatform::AEnvPlatform()
{
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

	// Ensure the actor replicates
	bReplicates = true;
	// dormant initial
	NetDormancy = ENetDormancy::DORM_DormantPartial;
	// Net Update Frequency
	SetNetUpdateFrequency(2.0f);
	SetMinNetUpdateFrequency(1.0f);

	
	// Create base mesh
	BaseMesh = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("BaseMesh"));
	RootComponent = BaseMesh;
	//BaseMesh->SetVisibility(false); // Start hidden

	 // Create Trigger Component to spawn another platforms
	SpawnNextPlatformTrigger = CreateDefaultSubobject<UBoxComponent>(TEXT("SpawnNextPlatformTrigger"));
	SpawnNextPlatformTrigger->SetupAttachment(RootComponent);

	// Set default size & visibility
	SpawnNextPlatformTrigger->SetBoxExtent(FVector(32.0f, 600.0f, 700.f));  // Default size
	SpawnNextPlatformTrigger->SetRelativeLocation(FVector(-200.0f, 0.0f, 680.0f));
	SpawnNextPlatformTrigger->SetCollisionEnabled(ECollisionEnabled::QueryOnly);


	// Create Trigger Component For returning platform back to pool
	ReturnToPoolTrigger = CreateDefaultSubobject<UBoxComponent>(TEXT("ReturnToPoolTrigger"));
	ReturnToPoolTrigger->SetupAttachment(RootComponent);

	ReturnToPoolTrigger->SetBoxExtent(FVector(32.0f, 600.0f, 700.f));  // Default size
	ReturnToPoolTrigger->SetRelativeLocation(FVector(1000.0f, 0.0f, 680.0f));
	ReturnToPoolTrigger->SetCollisionEnabled(ECollisionEnabled::QueryOnly);

	// Create PowerUp Spawn Root
	PowerUpSpawnRoot = CreateDefaultSubobject<USceneComponent>(TEXT("PowerUpSpawnRoot"));
	PowerUpSpawnRoot->SetupAttachment(RootComponent);

	// Create Shard Spline Root
	ShardTrailSplineRoot = CreateDefaultSubobject<USceneComponent>(TEXT("ShardTrailSplineRoot"));
	ShardTrailSplineRoot->SetupAttachment(RootComponent);
}

void AEnvPlatform::PostInitializeComponents()
{
	Super::PostInitializeComponents();
	// Spawn next platform trigger
	if (SpawnNextPlatformTrigger)
	{
		SpawnNextPlatformTrigger->OnComponentBeginOverlap.AddDynamic(this, &AEnvPlatform::OnNextSpawnTriggerOverlap);
	}
	else {UE_LOG(LogTemp, Error, TEXT("Trigger is null!"));}

	// Return to Pool trigger
	if (ReturnToPoolTrigger)
	{
		ReturnToPoolTrigger->OnComponentBeginOverlap.AddDynamic(this, &AEnvPlatform::OnReturnToPoolTriggerOverlap);
	}
	else {UE_LOG(LogTemp, Error, TEXT("Return to Pool Trigger is null!"));}
	
	// Retrieve all power-up spawners from the root component
	RetrievePowerUpSpawnersCompenents();

	// Retrieve all shard trail splines from the root component
	RetrieveShardTrailSplinesComponents();
	
}

bool AEnvPlatform::IsNetRelevantFor(const AActor* RealViewer, const AActor* ViewTarget,
	const FVector& SrcLocation) const
{
	bool bIsRelevant = Super::IsNetRelevantFor(RealViewer, ViewTarget, SrcLocation);

	// Ensure ViewTarget is valid and tagged as a jet
	if (!ViewTarget || !ViewTarget->ActorHasTag(TEXT("Jet")))
	{
		return bIsRelevant;
	}

	if (IsHidden()) return  bIsRelevant;
	
	// Example: Check if platform is ahead of the jet
	const float JetX = ViewTarget->GetActorLocation().X;
	const float PlatformX = GetActorLocation().X;

	if (PlatformX > JetX)  // ahead
	{
		const float Dist = FMath::Abs(PlatformX - JetX);
		
		bIsRelevant = Dist < MaxRelevantDistanceForReplication;
	}
	// const float Dist = FVector::Dist2D(GetActorLocation(), ViewTarget->GetActorLocation());
	// bIsRelevant = Dist < MaxRelevantDistanceForReplication;

	return bIsRelevant;
}


// Called when the game starts or when spawned
void AEnvPlatform::BeginPlay()
{

	Super::BeginPlay();

	// Bind to spawn trigger delegate
	if (AF1ghterFlyGameState* F1ghterFlyGameState = GetWorld()->GetGameState<AF1ghterFlyGameState>())
	{
		F1ghterFlyGameState->OnActivateNextPlatforms.AddDynamic(this, &AEnvPlatform::OnActivatePlatform);
	}
	else {UE_LOG(LogTemp, Warning, TEXT("EnvPlatform %s failed to find AF1ghterFlyGameState"), *GetName());}
}

void AEnvPlatform::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME(AEnvPlatform, SequenceNumber);
	DOREPLIFETIME(AEnvPlatform, PlatformType);
	DOREPLIFETIME(AEnvPlatform, ObstacleVariation);
}


void AEnvPlatform::PreparePlatform(FPlatformTraits Traits, int16 SequenceNo)
{
	PlatformType = Traits.PlatformType;
	ObstacleVariation = Traits.ObstacleVariation;
	SequenceNumber = SequenceNo;
}

void AEnvPlatform::SetPlatformLocation(FVector Location) {
	SetActorLocation(Location);

	// Set next spawn location translation
	InitialSpawnLocation = GetSpawnLocationBox()->GetComponentTransform().GetTranslation();
}

void AEnvPlatform::OnNextSpawnTriggerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{

	if (OtherActor && OverlappedComponent)
	{
		// Here you can handle the collision logic
		AF1ghterFlyCharacter* Jet = Cast<AF1ghterFlyCharacter>(OtherActor);

		if (Jet) 
		{

			FClientTriggerNextPlatformSpawn TriggerData;
			TriggerData.TriggerPosX = GetSpawnTrigger()->GetComponentTransform().GetTranslation().X;
			TriggerData.SequenceNo = SequenceNumber;


			// Locally control client logic  OR Server/standalone
			if (Jet->IsLocallyControlled() || HasAuthority()) {
			
				// Notify the game state to broadcast the event to active next platforms events, etc..
				if (AF1ghterFlyGameState* F1ghterFlyGameState = GetWorld()->GetGameState<AF1ghterFlyGameState>())
				{
					F1ghterFlyGameState->BroadcastNextPlatformToActivate(SequenceNumber);
				}
			}

			// Server/Standalone spawn logic
			if(Jet->HasAuthority()) {
				
				AF1ghterFlyGameMode* F1ghterFlyGameMode = Cast<AF1ghterFlyGameMode>(GetWorld()->GetAuthGameMode());
				if (F1ghterFlyGameMode)
				{
					F1ghterFlyGameMode->HandlePlatformSpawnRequest({
						Jet->Controller,
						TriggerData.SequenceNo,
						TriggerData.TriggerPosX
					});
				}
			}
			
		}
		
	}
}

void AEnvPlatform::OnReturnToPoolTriggerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{

	if (OtherActor && OverlappedComponent)
	{
		AF1ghterFlyCharacter* Jet = Cast<AF1ghterFlyCharacter>(OtherActor);
		if (Jet) {
			
			if (Jet->IsLocallyControlled() && GetNetMode() == NM_Client) {
				// Deactivate the platform
				if (AF1ghterFlyGameState* F1ghterFlyGameState = GetWorld()->GetGameState<AF1ghterFlyGameState>())
				{
					F1ghterFlyGameState->GetSpawnManager()->ReturnPlatformToPool(this);
				}
			}			
			// Server/Standalone logic to verify if it needs to return to pool as well.
			else if (Jet->HasAuthority()) {
				if (AF1ghterFlyGameMode* F1ghterFlyGameMode = Cast<AF1ghterFlyGameMode>(GetWorld()->GetAuthGameMode()))
				{
					F1ghterFlyGameMode->HandleDeactivatingPlatform(this);
				}
			}
		}
		
	}

}

void AEnvPlatform::RetrieveShardTrailSplinesComponents()
{
	// Clear the array to avoid duplicates
	ShardTrailSplines.Empty();

	// Get all child components of the ShardSplineRoot
	TArray<USceneComponent*> SplineComponents;

	if (ShardTrailSplineRoot)
	{
		ShardTrailSplineRoot->GetChildrenComponents(false, SplineComponents);

		for (USceneComponent* Child : SplineComponents)
		{
			if (USplineComponent* Spline = Cast<USplineComponent>(Child))
			{
				if (Spline->ComponentHasTag(FName("ShardTrailSpline")))
				{
					ShardTrailSplines.Add(Spline);
				}
			}
		}
	}
	else {
		UE_LOG(LogTemp, Error, TEXT("ShardTrailSplineRoot is null!"));
	}
}

void AEnvPlatform::RetrievePowerUpSpawnersCompenents()
{
	PowerUpSpawners.Empty();
	TArray<USceneComponent*> ChildComponents;
	if (PowerUpSpawnRoot) {
		PowerUpSpawnRoot->GetChildrenComponents(false, ChildComponents);
		for (USceneComponent* Child : ChildComponents)
		{
			if (Child->ComponentHasTag(FName("PowerUpSpawner")))
			{
				PowerUpSpawners.Add(Child);
			}
		}
	}
	else {
		UE_LOG(LogTemp, Error, TEXT("PowerUpSpawnRoot is null!"));
	}
}

void AEnvPlatform::DisablePlatform()
{
	SetActorHiddenInGame(true);
	DisableActorAndComponentCollisions();

	HasActivePowerUps = false;

	//SetActorLocation(FVector(0.0f, 0.0f, 0.0f));
}
void AEnvPlatform::EnablePlatform(bool skipCollisionActivation)
{
	SetActorHiddenInGame(false);

	// Do not enable platform and obstacles collider and overlap event yet.
	if (!skipCollisionActivation) {
		EnableActorAndComponentCollisions();

	}
}

void AEnvPlatform::ShouldActivateSpawnTrigger(bool IsActive)
{
	SpawnNextPlatformTrigger->SetCollisionEnabled(IsActive ? ECollisionEnabled::QueryOnly : ECollisionEnabled::NoCollision);
}

void AEnvPlatform::DisableActorAndComponentCollisions()
{
	// Disable collision for the actor itself
	//SetActorEnableCollision(false);

	// Disable collision for all child components
	TArray<UActorComponent*> Components;
	GetComponents(Components);
	for (UActorComponent* Component : Components)
	{
		if (UPrimitiveComponent* PrimitiveComp = Cast<UPrimitiveComponent>(Component))
		{
			//UE_LOG(LogTemp, Warning, TEXT("HERE %s"), *PrimitiveComp->GetName());
			//PrimitiveComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);
			
		}
		// Check if the component is a UChildActorComponent
		if (UChildActorComponent* ChildActorComponent = Cast<UChildActorComponent>(Component))
		{
			// Get the child actor and cast it to ADamageDealer
			if (AActor* ChildActor = ChildActorComponent->GetChildActor())
			{
				if (ADamageDealer* DamageDealerObs = Cast<ADamageDealer>(ChildActor))
				{
					DamageDealerObs->SetDamageOverlapEnabled(false); // not need listen to overlap events if disable
				}
			}
		}
	}
}

void AEnvPlatform::EnableActorAndComponentCollisions()
{
	// Enable collision for the actor itself
	//SetActorEnableCollision(true);

	// Disable collision for all child components
	TArray<UActorComponent*> Components;
	GetComponents(Components);
	for (UActorComponent* Component : Components)
	{
		if (UPrimitiveComponent* PrimitiveComp = Cast<UPrimitiveComponent>(Component))
		{
			//UE_LOG(LogTemp, Warning, TEXT("HERE %s"), *PrimitiveComp->GetName());
			//PrimitiveComp->SetCollisionEnabled(ECollisionEnabled::NoCollision);

			// figure out to do this better. becuase the triggerContainer has a different collision type than the platform objects

		}

		// Check if the component is a UChildActorComponent
		if (UChildActorComponent* ChildActorComponent = Cast<UChildActorComponent>(Component))
		{
			
			// Get the child actor and cast it to ADamageDealer
			if (AActor* ChildActor = ChildActorComponent->GetChildActor())
			{
				// if component has tag of obstacles, set their owner to be this platform
				if (ChildActor->ActorHasTag(FName("Obstacle"))) {
					ChildActor->SetOwner(this); // being use for owner relevancy currently
				}
				
				if (ADamageDealer* DamageDealerObs = Cast<ADamageDealer>(ChildActor))
				{
					DamageDealerObs->SetDamageOverlapEnabled(true); // Enable Overlap events on obstacles
				}
			}
		}
	}
}


/// <summary>
///  Activate a platforms collisions and overlap events.
/// </summary>
/// <param name="TriggeredSequenceNumber"></param>
void AEnvPlatform::OnActivatePlatform(int32 TriggeredSequenceNumber)
{
	// if the triggered sequence number is the same as the current one, do nothing
	if (SequenceNumber <= TriggeredSequenceNumber || IsHidden()) return;

	// Server/ Standalone
	if (HasAuthority()) {
		// This platform is the next platform jet will go through
		if (SequenceNumber == (TriggeredSequenceNumber + 1)) {

			// Enable collisions for the actor itself
			EnableActorAndComponentCollisions();

		}
	}
}