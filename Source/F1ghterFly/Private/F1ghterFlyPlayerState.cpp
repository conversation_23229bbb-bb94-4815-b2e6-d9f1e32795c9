// Fill out your copyright notice in the Description page of Project Settings.


#include "F1ghterFlyPlayerState.h"
#include "Net/UnrealNetwork.h"
#include "SpawnManager.h"
#include "ActorComponents/JetEventManager.h"
#include "F1ghterFlyCharacter.h"
#include "ActorComponents/EffectManager.h"
#include "ActorComponents/ScoreManager.h"

AF1ghterFlyPlayerState::AF1ghterFlyPlayerState()
{
   
}

void AF1ghterFlyPlayerState::BeginPlay()
{
	Super::BeginPlay();

	// Health setup
	MaxHealth = 100.f;
	Health = MaxHealth;

}

void AF1ghterFlyPlayerState::BeginDestroy()
{
	Super::BeginDestroy();
	UE_LOG(LogTemp, Log, TEXT("Jet PLAYER STATE Destroyed: %s , netmode: %d , hasAuthority: %s"),
		*GetName(),
		static_cast<int32>(GetNetMode()),
		HasAuthority() ? TEXT("TRUE") : TEXT("FALSE"));
}

void AF1ghterFlyPlayerState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Ensure those trackers gets replicated to client
    DOREPLIFETIME(AF1ghterFlyPlayerState, Health);
	DOREPLIFETIME_CONDITION(AF1ghterFlyPlayerState, CurrentScore, COND_OwnerOnly); // Replicate Score only to the owner
	DOREPLIFETIME_CONDITION(AF1ghterFlyPlayerState, HypeMeterProgress, COND_OwnerOnly); // Replicate HyperMeterProgress only to the owner
	DOREPLIFETIME_CONDITION(AF1ghterFlyPlayerState, HypeRank, COND_OwnerOnly); // Replicate HypeRank only to the owner
	DOREPLIFETIME_CONDITION(AF1ghterFlyPlayerState, ScoreMultiplier, COND_OwnerOnly); // Replicate ScoreMultiplier only to the owner
}

// Increase Health. If not provided, it will be MaxHealth
void AF1ghterFlyPlayerState::IncreaseHealth(int16 AddedHealth) 
{

	if (!HasAuthority()) return;

	Health  = FMath::Max(AddedHealth ? Health + AddedHealth: MaxHealth, MaxHealth);

	// Only standalone
	if (GetNetMode() == NM_Standalone || GetNetMode() == NM_DedicatedServer) {
		OnRep_Health();
	}

}

void AF1ghterFlyPlayerState::ReduceHealth(float Damage)
{
	if (bIsGameOver)
	{
		return; // Already dead
	}

	if (!HasAuthority()) return;

	// Update Health of player
	Health = FMath::Max(0.f, Health - Damage);

	if (HasAuthority()) {
		OnRep_Health();
	}
}

void AF1ghterFlyPlayerState::ApplyDamage(float DamageAmount, EJetBodyPart BodyPart, float HypeMeterDecayAmount)
{
	if (bIsGameOver)
	{
		return; // Already dead
	}

	// Get the character for shield checks and event broadcasting
	AF1ghterFlyCharacter* JetCharacter = Cast<AF1ghterFlyCharacter>(GetPawn());
	if (!JetCharacter)
	{
		return; // No valid character
	}

	// Check for shields before applying any damage
	if (JetCharacter->GetEffectManager() && JetCharacter->GetEffectManager()->IsJetShielding())
	{
		return; // Shielded, no damage or events
	}

	// Server authority: Apply actual damage and hype meter decay
	if (HasAuthority())
	{
		// Apply health damage
		ReduceHealth(DamageAmount);

		// Apply hype meter decay
		if (JetCharacter->GetScoreManager())
		{
			JetCharacter->GetScoreManager()->DecayHypeMeter(HypeMeterDecayAmount);
		}
	}

	// Client-side cosmetic events (standalone, autonomous proxy, simulated proxy)
	if (GetNetMode() == NM_Standalone || GetLocalRole() != ROLE_Authority)
	{
		if (JetCharacter->GetJetEventManager())
		{
			JetCharacter->GetJetEventManager()->BroadcastDamageTaken(DamageAmount);
		}
	}
}

void AF1ghterFlyPlayerState::OnRep_Health()
{
	// Only clients
	if (GetNetMode() != NM_DedicatedServer) {
		if(AF1ghterFlyCharacter* JetCharacter = Cast<AF1ghterFlyCharacter>(GetPawn()))
		{
			// Broadcast the health change.
			JetCharacter->GetJetEventManager()->BroadcastHealthChanged(Health);

			// if not server, broadcast event if health is below a certain threshold
			if ((Health / MaxHealth) < 0.5f) {
				// Broadcast low health event
			
				JetCharacter->GetJetEventManager()->BroadcastLowHealth(Health);
			}
		}
	}

	if (Health <= 0.f && !bIsGameOver)
	{
		// bIsGameOver = true;
		OnGameOver();
	}
	
}
void AF1ghterFlyPlayerState::OnGameOver()
{
	UE_LOG(LogTemp, Warning, TEXT("GAME OVER - Health Updated: %.1f, Role: %d"), Health, (int32)GetLocalRole());
}

void AF1ghterFlyPlayerState::OnRep_CurrentScore()
{
	// Only the locally controlled player should receive this update to update its UI
	if (GetNetMode() != NM_DedicatedServer) {
		OnScoreUpdated.Broadcast(CurrentScore);
	}
}

void AF1ghterFlyPlayerState::OnRep_HypeMeterProgress()
{
	// Only the locally controlled player should receive this update to update its UI
	if (GetNetMode() != NM_DedicatedServer) {
		OnHypeMeterUpdated.Broadcast();
	}
}

void AF1ghterFlyPlayerState::OnRep_HypeRank()
{
	if(GetNetMode() == NM_DedicatedServer) return;

	// If a player achieves Rank S, we need to show special effects/vfx/UI
	if(HypeRank == EHypeRank::RankS)
	{
		// Display popup feedback
		if(AF1ghterFlyCharacter* JetCharacter = Cast<AF1ghterFlyCharacter>(GetPawn()); IsValid(JetCharacter->GetJetEventManager()))
		{
			JetCharacter->GetJetEventManager()->BroadcastReachRankS(RANK_S_BONUS_SCORE);
		}

	}
}

