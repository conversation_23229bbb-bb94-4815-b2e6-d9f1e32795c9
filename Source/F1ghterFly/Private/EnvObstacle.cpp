// Fill out your copyright notice in the Description page of Project Settings.


#include "EnvObstacle.h"

// Sets default values
AEnvObstacle::AEnvObstacle()
{
	bReplicates = true;
	SetReplicateMovement(true);

	// set net owner relevancy
	bNetUseOwnerRelevancy = true;
	
 	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = false;

}

// Called when the game starts or when spawned
void AEnvObstacle::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void AEnvObstacle::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

void AEnvObstacle::OnDamageOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{
	// Call base function if needed
	Super::OnDamageOverlap(OverlappedComponent, OtherActor, OtherComp, OtherBodyIndex, bFromSweep, SweepResult);

}
