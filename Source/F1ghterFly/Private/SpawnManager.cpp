// Fill out your copyright notice in the Description page of Project Settings.


#include "SpawnManager.h"
#include "EnvPlatform.h"
#include "F1ghterFlyGameState.h"
#include "Engine/World.h"
#include "F1ghterFly/Types/PlatformTypes.h"
#include <PlatformUtils.h>
#include <Resources/AetherShard.h>
#include "Subsystems/PowerUpSystem.h"

ASpawnManager::ASpawnManager()
{
}
void ASpawnManager::PostInitializeComponents()
{
    Super::PostInitializeComponents();

}

void ASpawnManager::BeginPlay()
{
    Super::BeginPlay();
    SeedManager = GetGameInstance()->GetSubsystem<USeedManager>();
    // Initialize Assets
    InitializeAssets();
}

void ASpawnManager::Tick(float DeltaTime)
{
    
}

// Called by the GameState by a GameMode Mulitcast RPC after gamemode has been initialize fully
void ASpawnManager::InitializeAssets()
{
    LoadPlatformAssetsForTheme(EWorldTheme::Greece); // Initial theme platform assets
	LoadCollectibleAssets(); // Load collectible assets
}

void ASpawnManager::StartInitializingAllPools()
{
    // Early exit if pool is already initialized
    if(PlatformPool.Num() > 0) return;
    
    InitializePool();
    InitializeCollectiblePool();  
}

#pragma region Platforms Logic


void ASpawnManager::InitializePool()
{
    if (LoadedPlatformAssets.Num() == 0) {
        UE_LOG(LogTemp, Error, TEXT("SpawnManger: Loaded platform assets are empty"));
        return;
    }

    for (int32 i = 0; i < PoolSize; i++)
    {
        int16 SequenceNo = i + 1;

        FPlatformTraits Traits = SeedManager->GetNextPlatformTraits(SequenceNo);
        AEnvPlatform* Platform = GetPooledPlatform(Traits);

        if (Platform) {
            // away from origin
            Platform->SetActorLocation(FVector(-30000.0f, 0.0f, -20000.0f));
            Platform->PreparePlatform(Traits, SequenceNo);
            Platform->DisablePlatform(); // To-do: hide and disable platforms and components collisions
        }

    }
}

// Spawns the intial platform based on the sequence number
AEnvPlatform* ASpawnManager::SpawnInitialPlatform()
{
    FPlatformTraits Traits = { EPlatformType::Columns, 1 };

    if (AEnvPlatform* Platform = GetPooledPlatform(Traits)) {
        float SpawnPosX = 20000.0f;
        int32 InitialSequenceNumber = 1;
        
        Platform->PreparePlatform(Traits, InitialSequenceNumber);
        Platform->EnablePlatform();
        // Set position 
        Platform->SetPlatformLocation(FVector(SpawnPosX, 0.0f, 0.0f));

        return Platform;
    }
    return nullptr;
}

// Spawns the next platform based on the sequence number
AEnvPlatform* ASpawnManager::SpawnNextPlatform(const FVector& SpawnPosition, int16 SequenceNo)
{
    if (SeedManager)
    {
        FPlatformTraits Traits = SeedManager->GetNextPlatformTraits(SequenceNo);
        AEnvPlatform* Platform = GetPooledPlatform(Traits);

        if (Platform) {
            Platform->PreparePlatform(Traits, SequenceNo);
            Platform->EnablePlatform();
            // Set position 
            Platform->SetPlatformLocation(SpawnPosition);
        }
		return Platform;
    }
	else
	{
		UE_LOG(LogTemp, Error, TEXT("SpawnManager: SeedManager is not set"));
		return nullptr;
	}
}

TArray<AEnvPlatform*> ASpawnManager::SpawnMultiplePlatforms(FVector LastPlatformSpawnLocation, int16 StartSequenceNo, int16 Amount)
{
    if (!SeedManager)
        return TArray<AEnvPlatform*>();

    TArray<AEnvPlatform*> NewPlatforms;

    //UE_LOG(LogTemp, Error, TEXT("LastPlatformSpawnLocation: %f"), LastPlatformSpawnLocation.X);

    for (int16 i = 0; i < Amount; i++)
    {
        int16 SequenceNo = StartSequenceNo + i;
        FPlatformTraits Traits = SeedManager->GetNextPlatformTraits(SequenceNo);
        AEnvPlatform* Platform = GetPooledPlatform(Traits);

        if (Platform) {

            Platform->PreparePlatform(Traits, SequenceNo);
            Platform->EnablePlatform(true);

            // Set position 
            Platform->SetPlatformLocation(LastPlatformSpawnLocation);

            LastPlatformSpawnLocation = Platform->GetSpawnLocation();

            NewPlatforms.Add(Platform);
        }
    }

    return NewPlatforms;
}

AEnvPlatform* ASpawnManager::GetPooledPlatform(FPlatformTraits Traits)
{
    AEnvPlatform* PlatformWithSameType = nullptr;

    for (AEnvPlatform* Platform : PlatformPool)
    {
        // only get platform if it is in the pool.
        if (Platform->IsHidden() && Platform->GetPlatformType() == Traits.PlatformType) {
            PlatformWithSameType = Platform;

            if (Platform->GetObstacleVariation() == Traits.ObstacleVariation) {
                return Platform;
            }
        }
    }

    // If pool is full, spawn a new one (optional)
    TSubclassOf<AEnvPlatform> PlatformClass = GetPlatformClassAsset(Traits.PlatformType, Traits.ObstacleVariation);
    AEnvPlatform* NewPlatform = nullptr;

    // incase this is nullptr, just pick the one first
    if (PlatformClass == nullptr) {
       // UE_LOG(LogTemp, Error, TEXT("SpawnManger: Platform Asset was not found with the given platform type: %d and variation: %d"), Traits.PlatformType, Traits.ObstacleVariation);

        // If atleast the type is correct lets return it.
        if (PlatformWithSameType != nullptr) {
            return PlatformWithSameType;
        }
        PlatformClass = LoadedPlatformAssets.Num() > 0 ? LoadedPlatformAssets[0] : nullptr;
    }


    if (PlatformClass)
    {
        NewPlatform = GetWorld()->SpawnActor<AEnvPlatform>(PlatformClass);
        PlatformPool.Add(NewPlatform);
    }

    return NewPlatform;
}

// Returns the platform back to the pool. As well as any collectibles associated with it such as the Aether shards, power-ups, etc...
void ASpawnManager::ReturnPlatformToPool(AEnvPlatform* Platform)
{
	if (Platform && !Platform->IsHidden())
	{
		Platform->DisablePlatform();

	    // TO-DO this can be move to the platform itself.
        // return collectibles in their pool as well
	    ReturnCollectiblesToPoolBySequenceNumber(Platform->GetSequenceNumber());

	}
}

void ASpawnManager::ReturnPlatformToPoolBySequenceNumber(int32 SequenceNumber)
{
    if (AEnvPlatform* Platform = FPlatformUtils::GetPlatformWithSequenceNumber(PlatformPool,SequenceNumber)) {
        ReturnPlatformToPool(Platform);
    }

}

void ASpawnManager::QueueActivePlatformSpawn(int32 SequenceNumber)
{
    if (!QueuedSequenceNumbers.Contains(SequenceNumber))
    {
        ActivePlatformSpawnQueue.Enqueue(SequenceNumber);
        QueuedSequenceNumbers.Add(SequenceNumber);
        // Start draining if not already
        if (!GetWorld()->GetTimerManager().IsTimerActive(SpawnTickTimer))
        {
            // Spawn platforms every 0.5 second
            GetWorld()->GetTimerManager().SetTimer(SpawnTickTimer, this, &ASpawnManager::DrainActivePlatformSpawnQueue, 0.5f, true);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Sequence %d is already in the queue."), SequenceNumber);
    }
}

void ASpawnManager::DrainActivePlatformSpawnQueue()
{
    if (!ActivePlatformSpawnQueue.IsEmpty())
    {
        int32 NextSequenceNumber;
        ActivePlatformSpawnQueue.Dequeue(NextSequenceNumber);
        QueuedSequenceNumbers.Remove(NextSequenceNumber);
        
        // SPAWN PLATFORM, COLLECTIBLES, ETC... AND UPDATE ACTIVE PLATFORM ARRAY !
        AuthSpawnPlatformWithContentsAndReplicate(NextSequenceNumber);
    }
    else {
        // If the queue is empty, stop the timer
        GetWorld()->GetTimerManager().ClearTimer(SpawnTickTimer);
        //UE_LOG(LogTemp, Warning, TEXT("SpawnManager: Platform spawn queue is empty, stopping timer."));
    }
}

void ASpawnManager::AuthSpawnPlatformWithContentsAndReplicate(int16 NextSequenceNumberToSpawn)
{
    if (!HasAuthority()) return;

    // Get GameState
    AF1ghterFlyGameState* F1ghterFlyGameState = GetWorld()->GetGameState<AF1ghterFlyGameState>();

    if (!F1ghterFlyGameState) { UE_LOG(LogTemp, Error, TEXT("SpawnManager: AuthSpawnPlatformWithContentsAndReplicate: F1ghterFlyGameState not found")); return; };

    TArray<AEnvPlatform*> ActivePlatformPool = FPlatformUtils::GetAllActivePlatforms(GetPlatformPool());

    // Find the Max Sequence Number
    int32 MaxSequenceNumber = 1;
    for (AEnvPlatform* Platform : ActivePlatformPool)
    {
        MaxSequenceNumber = FMath::Max(MaxSequenceNumber, Platform->GetSequenceNumber());
    }

    // Get the last Platform
    AEnvPlatform* LastPlatform = FPlatformUtils::GetPlatformWithSequenceNumber(ActivePlatformPool, MaxSequenceNumber);
	if (LastPlatform == nullptr) { UE_LOG(LogTemp, Error, TEXT("SpawnManager: AuthSpawnPlatformWithContentsAndReplicate: LastPlatform not found. MaxSequenceNumber = %d"), MaxSequenceNumber); return; };

    FVector LastPlatformSpawnLocation = LastPlatform->GetSpawnLocation();

    // SERVER/STANDALONE: Spawn multiple platforms directly
    AEnvPlatform* NewPlatform = SpawnNextPlatform(LastPlatformSpawnLocation, NextSequenceNumberToSpawn);
    
    // UPDATE the ActivePlatforms array.
    F1ghterFlyGameState->ActivePlatforms.AddPlatform(
        NewPlatform->GetSequenceNumber(),
        NewPlatform->GetActorLocation()
    );
    
    #pragma region Power ups Spawn logic
    // Number of powerups to spawn
    int16 PowerUpSpawnAmount = 3;
    
    TArray<FPowerUpInfo> PowerUpInfo;
    if (UPowerUpSystem *PowerUpSystem = F1ghterFlyGameState->GetPowerUpSystem()) {

        // Get the power-up spawner indexes
        const TArray<USceneComponent*>& PowerUpSpawners = NewPlatform->GetPowerUpSpawners();

		// Randomly select power-up spawner indices
        TArray<int32> SelectedSpawnIndices;
        int32 NumIndicesToSelect = FMath::Min(PowerUpSpawnAmount, PowerUpSpawners.Num());

        while (SelectedSpawnIndices.Num() < NumIndicesToSelect)
        {
            int32 RandomIndex = FMath::RandRange(0, PowerUpSpawners.Num() - 1);
            if (!SelectedSpawnIndices.Contains(RandomIndex))
            {
                SelectedSpawnIndices.Add(RandomIndex);
            }
        }
        
        TArray<EPowerUpType> PreviousPowerUps;
        // Get the power-ups and associate them with the spawn indices
        if (SelectedSpawnIndices.Num() > 0)
        {
            // 3 powerups max per platform
            for (int16 i = 0; i < SelectedSpawnIndices.Num(); i++)
            {
                // Get the powerUp type
                EPowerUpType PowerUpType = PowerUpSystem->GetNextPowerUpType(NewPlatform->GetPlatformType(), PreviousPowerUps);
                PreviousPowerUps.Add(PowerUpType);

                // store the powerup type and spawn index
                PowerUpInfo.Add({ PowerUpType, SelectedSpawnIndices[i] });
            }
        }
	}
	else {
		UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState: HandlePlatformSpawnRequest: PowerUpSystem not found / cast propertly"));
	}
	#pragma endregion

    #pragma region Aether Shard Spawn logic
	
    // Get the shard spline spawners index to spawn aether shards
    TArray<int32> SelectedShardSplineIndices;

	const TArray<USplineComponent*>& ShardSplines = NewPlatform->GetShardTrailSplines();
    
    if (ShardSplines.Num() > 0)
    {
        int32 NumIndicesToSelect = FMath::Min(PowerUpSpawnAmount, ShardSplines.Num());

        while (SelectedShardSplineIndices.Num() < NumIndicesToSelect)
        {
            int32 RandomIndex = FMath::RandRange(0, ShardSplines.Num() - 1);
            if (!SelectedShardSplineIndices.Contains(RandomIndex))
            {
                SelectedShardSplineIndices.Add(RandomIndex);
            }
        }
    }
	#pragma endregion
    
    // SERVER/STANDALONE: Spawn POWER-UPS for each platform      
    SpawnPowerUps(PowerUpInfo, NewPlatform);

	// SERVER/STANDALONE: Spawn Aether Shards for each platform
    SpawnAetherShards(SelectedShardSplineIndices, NewPlatform);
}

// Correct the location of the platforms on the client side. 
//void ASpawnManager::ServerPlatformLocationCorrection(TArray<FActivePlatformData> ActivePlatforms)
//{
//    for (FActivePlatformData ActivePlatform : ActivePlatforms)
//    {
//        for (AEnvPlatform* Platform : PlatformPool)
//        {
//            // Check if the platform is not hidden and has the same sequence number, then update the location
//            if (!Platform->IsHidden() && Platform->GetSequenceNumber() == ActivePlatform.SequenceNumber)
//            {
//                Platform->SetActorLocation(FVector(ActivePlatform.SpawnPosX, 0.0f, 0.0f));
//                break;
//            }
//        }
//    }
//}

TSubclassOf<AEnvPlatform> ASpawnManager::GetPlatformClassAsset(EPlatformType Type, int32 ObstacleVariation)
{
    for (TSubclassOf<AEnvPlatform> PlatformClass : LoadedPlatformAssets)
    {
        if (PlatformClass)
        {
            // Get the default object to access its properties
            AEnvPlatform* DefaultPlatform = PlatformClass->GetDefaultObject<AEnvPlatform>();
            if (DefaultPlatform && DefaultPlatform->GetPlatformType() == Type && DefaultPlatform->GetObstacleVariation() == ObstacleVariation)
            {
                return PlatformClass;
            }
        }
    }
    return nullptr;
}

// Called by the GameMode to update the platform assets for the new theme
void ASpawnManager::LoadPlatformAssetsForTheme(EWorldTheme NewTheme)
{

    LoadedPlatformAssets.Empty();

    if (!PlatformThemeDataTable)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: PlatformThemeDataTable is not set"));
        return;
    }

    FString EnumString = StaticEnum<EWorldTheme>()->GetDisplayNameTextByValue(static_cast<int64>(NewTheme)).ToString();
    FName RowName = FName(*EnumString);

    const FPlatformThemeData* ThemeData = PlatformThemeDataTable->FindRow<FPlatformThemeData>(RowName, TEXT("SpawnManager"));
    if (ThemeData)
    {
        LoadedPlatformAssets = ThemeData->PlatformClasses;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: No data found for theme %d"), static_cast<uint8>(NewTheme));
    }

    if (LoadedPlatformAssets.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: Loaded platform assets are empty"));
    }

}

#pragma endregion

#pragma region Collectibles Logic

void ASpawnManager::InitializeCollectiblePool()
{
    if (LoadedCollectibleAssets.Num() == 0) {
        UE_LOG(LogTemp, Error, TEXT("SpawnManger: Loaded collectible assets are empty"));
        return;
    }

	// Populate Aether Shards in collectible pool
    PopulateAetherShardInCollectiblePool();

	// Populate the collectible pool with initial power-ups
    PopulatePowerUpInCollectiblePool();
   

}

// Populate the some aether shards in the collectible pool
void ASpawnManager::PopulateAetherShardInCollectiblePool()
{
    // Spawning Aether Shards
    TSubclassOf<ABaseCollectible> CollectibleClass = GetCollectibleClassAsset(ECollectibleType::Coin);
    if (!CollectibleClass)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: Collectible Asset was not found"));
        return; // Exit early if not found
    }

    // Spawn 20 shards initiallly and add to the pool
    for (int32 i = 0; i < 20; i++)
    {
        ABaseCollectible* CollectibleActor = GetWorld()->SpawnActor<ABaseCollectible>(CollectibleClass);

        if (CollectibleActor)
        {
            CollectiblePool.Add(CollectibleActor);

            // Move away from origin
            CollectibleActor->SetActorLocation(FVector(-30000.0f, 0.0f, -20000.0f));
            CollectibleActor->DisableCollectible(); // Hide + disable collisions
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("SpawnManager: Failed to spawn AetherShard from class"));
        }
    }
}

// Populate the some power-ups in the collectible pool
void ASpawnManager::PopulatePowerUpInCollectiblePool()
{
   
    TArray<EPowerUpType> InitialPowerUps = {
        EPowerUpType::DoubleDash,
        EPowerUpType::EnergyShield,
        EPowerUpType::Health,
        EPowerUpType::MagnetField,
        EPowerUpType::RocketBarrage,
        EPowerUpType::ScoreMultiplier,
        EPowerUpType::TurboBoost
    };

    for (EPowerUpType PowerUpType : InitialPowerUps)
    {
        TSubclassOf<ABasePowerUp> PowerUpClass = GetPowerUpClassAsset(PowerUpType);
        ABasePowerUp* NewPowerUp = nullptr;

        // incase this is nullptr, just pick the one first
        if (PowerUpClass == nullptr) {
            UE_LOG(LogTemp, Error, TEXT("SpawnManger: PowerUp Asset was not found with the given POWER UP type: %d "), PowerUpType);

            PowerUpClass = LoadedCollectibleAssets.Num() > 0 ? LoadedCollectibleAssets[0] : nullptr;
        }

        if (PowerUpClass)
        {
            NewPowerUp = GetWorld()->SpawnActor<ABasePowerUp>(PowerUpClass);

            NewPowerUp->DisableCollectible(); // Disable the power-up after spawning
            NewPowerUp->SetActorLocation(FVector(-30000.0f, 0.0f, -20000.0f)); // away from origin

            CollectiblePool.Add(NewPowerUp);
        }
    }
}

void ASpawnManager::SpawnPowerUps(TArray<FPowerUpInfo> PowerUps, AEnvPlatform* Platform)
{
    // Get the powerup spawner
    const TArray<USceneComponent*>& AllPowerUpSpawners = Platform->GetPowerUpSpawners();

    // loop through powerups
    for (const FPowerUpInfo& PowerUp : PowerUps)
    {
        if (AllPowerUpSpawners.Num() == 0) return;

        USceneComponent* PowerUpSpawner = AllPowerUpSpawners[PowerUp.SpawnIndex];

        if (PowerUpSpawner)
        {
            // Spawn the powerup
            FVector SpawnLocation = PowerUpSpawner->GetComponentTransform().GetTranslation();

            ABasePowerUp* PowerUpActor = GetPooledPowerUp(PowerUp.PowerUpType);
            if (PowerUpActor) {
                // Associate the power-up with the platform
                PowerUpActor->SetPlatformSequenceNumber(Platform->GetSequenceNumber());
                PowerUpActor->SetCollectibleSpawnIndex(PowerUp.SpawnIndex);

                // Set the power-up location
                PowerUpActor->SetActorLocation(SpawnLocation);
                PowerUpActor->SetActorRotation(FRotator(0, 180, 0));

                // Enable the power-up
                PowerUpActor->EnableCollectible();

                // Keep a reference of the power-up in the platform
                Platform->HasActivePowerUps = true;
            }
        }
    }
}

void ASpawnManager::SpawnAetherShards(TArray<int32> SplineSpawnerIndexes, AEnvPlatform* Platform)
{
	// Get trail splines
	const TArray<USplineComponent*>& AllTrailSplines = Platform->GetShardTrailSplines();
	if (AllTrailSplines.Num() == 0) return;

	// loop throught the selectec spline spawners indexes
	for (int32 SplineIndex : SplineSpawnerIndexes)
	{
		if (SplineIndex < 0 || SplineIndex >= AllTrailSplines.Num()) continue;

		USplineComponent* SelectedSpline = AllTrailSplines[SplineIndex];

		if (SelectedSpline)
		{
            // Calculate number of shards based on spline length and spacing
            float SplineLength = SelectedSpline->GetSplineLength();
            int32 ShardCount = FMath::Clamp(FMath::CeilToInt(SplineLength / 200), 5, 10);

            for (int16 i = 0; i < ShardCount; i++)
            {
                float DistanceAlongSpline = i * (SplineLength / FMath::Max(1, ShardCount - 1)); // Even distribution
                FVector SpawnLocation = SelectedSpline->GetLocationAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);
                FRotator SpawnRotation = SelectedSpline->GetRotationAtDistanceAlongSpline(DistanceAlongSpline, ESplineCoordinateSpace::World);

                AAetherShard* ShardActor = Cast<AAetherShard>(GetPooledCollectible(ECollectibleType::Coin));
                if (ShardActor) {
                    // Associate the power-up with the platform
                    ShardActor->SetPlatformSequenceNumber(Platform->GetSequenceNumber());
                    ShardActor->SetCollectibleSpawnIndex(SplineIndex);

                    // Set the power-up location
                    ShardActor->SetActorLocation(SpawnLocation);
                    ShardActor->SetActorRotation(SpawnRotation);

                    // Enable the power-up
                    ShardActor->EnableCollectible();
                }

            }
		}
	}

}

ABasePowerUp* ASpawnManager::GetPooledPowerUp(EPowerUpType PowerUpType)
{

    for (ABaseCollectible* Collectible : CollectiblePool)
    {
        // Check if the collectible is a power-up and is hidden
        ABasePowerUp* PowerUp = Cast<ABasePowerUp>(Collectible);
        if (PowerUp && PowerUp->IsHidden() && PowerUp->GetPowerUpType() == PowerUpType)
        {
            //  UE_LOG(LogTemp, Error, TEXT("SpawnManager:  POOLED PowerUp, PowerUpType: %d"), PowerUpType);
            return PowerUp;
        }
    }

    // If pool is full, spawn a new one (optional)
    TSubclassOf<ABasePowerUp> PowerUpClass = GetPowerUpClassAsset(PowerUpType);
    ABasePowerUp* NewPowerUp = nullptr;

    // incase this is nullptr, just pick the one first
    if (PowerUpClass == nullptr) {
        UE_LOG(LogTemp, Error, TEXT("SpawnManger: PowerUp Asset was not found with the given POWER UP type: %d "), PowerUpType);

        PowerUpClass = LoadedCollectibleAssets.Num() > 0 ? LoadedCollectibleAssets[0] : nullptr;
    }

    if (PowerUpClass)
    {
        NewPowerUp = GetWorld()->SpawnActor<ABasePowerUp>(PowerUpClass);
        CollectiblePool.Add(NewPowerUp);

        //  UE_LOG(LogTemp, Error, TEXT("SpawnManager:  NEW PowerUp, PowerUpType: %d"), PowerUpType);
    }

    return NewPowerUp;
}

ABaseCollectible* ASpawnManager::GetPooledCollectible(ECollectibleType CollectibleType)
{
    for (ABaseCollectible* Collectible : CollectiblePool)
    {
        if (Collectible->GetCollectibleType() == ECollectibleType::Coin) {

			// Cast to AAetherShard
            if (Collectible->IsHidden())
            {
                return Collectible;
            }
        }
      
    }

    // If pool is full, spawn a new one (optional)
    TSubclassOf<ABaseCollectible> CollectibleClass = GetCollectibleClassAsset(CollectibleType);
    ABaseCollectible* Collectible = nullptr;

    // incase this is nullptr, just pick the one first
    if (CollectibleClass == nullptr) {
        UE_LOG(LogTemp, Error, TEXT("SpawnManger: CollectibleClass Asset was not found with the given CollectibleType: %d "), CollectibleType);

    }

    if (CollectibleClass)
    {
       Collectible = GetWorld()->SpawnActor<ABaseCollectible>(CollectibleClass);
    }

	// Add to the pool if created
	if (Collectible)
	{
		CollectiblePool.Add(Collectible);
	}

    return Collectible;
}

// Return the Collectibles (Aether Shards, Power-Ups, etc..) back in the pool based on Sequence number.
void ASpawnManager::ReturnCollectiblesToPoolBySequenceNumber(int32 SequenceNumber)
{
    // Early exit if pool is empty
    if (CollectiblePool.IsEmpty())
    {
        return;
    }

    const TArray<ABaseCollectible*> CollectiblesBySequence = FPlatformUtils::GetCollectiblesWithSequenceNumber(CollectiblePool, SequenceNumber);

    // Use range-based for loop with reference to avoid copying pointers
    for (ABaseCollectible* const Collectible : CollectiblesBySequence)
    {
        if (ensure(Collectible))
        {
            ReturnCollectibleToPool(Collectible);
        }
    }

}

void ASpawnManager::ReturnSpecificCollectibleToPool(int32 SequenceNumber, int32 SpawnIndex)
{
	// Early exit if pool is empty
	if (CollectiblePool.IsEmpty())
	{
		return;
	}

	const TArray<ABaseCollectible*> CollectiblesBySequence = FPlatformUtils::GetCollectiblesWithSequenceNumber(CollectiblePool, SequenceNumber);

	// Use range-based for loop with reference to avoid copying pointers
	for (ABaseCollectible* const Collectible : CollectiblesBySequence)
	{
		if (ensure(Collectible) && Collectible->GetCollectibleSpawnIndex() == SpawnIndex)
		{
			ReturnCollectibleToPool(Collectible);
			break; // Exit the loop after returning the specific collectible
		}
	}
}

void ASpawnManager::ReturnCollectibleToPool(ABaseCollectible* Collectible)
{
    if (Collectible && !Collectible->IsHidden())
    {
        Collectible->DisableCollectible();
    }
}

/// <summary>
///  Get Power-up Class Asset from the loaded assets.
/// </summary>
/// <param name="PowerUpType"></param>
/// <returns></returns>
TSubclassOf<ABasePowerUp> ASpawnManager::GetPowerUpClassAsset(EPowerUpType PowerUpType)
{
    for (TSubclassOf<ABaseCollectible> CollectibleClass : LoadedCollectibleAssets)
    {
        if (CollectibleClass)
        {
            // Get the default object to access its properties
            ABasePowerUp* DefaultCollectible = CollectibleClass->GetDefaultObject<ABasePowerUp>();
            if (DefaultCollectible)
            {
				if (DefaultCollectible->GetPowerUpType() == PowerUpType)
                    return TSubclassOf<ABasePowerUp>(CollectibleClass);
            }
        }
    }
    return nullptr;
}

/// <summary>
///  Get Collectible Class Asset from the loaded assets.
/// </summary>
/// <param name="CollectibleType"></param>
/// <returns></returns>
TSubclassOf<ABaseCollectible> ASpawnManager::GetCollectibleClassAsset(ECollectibleType CollectibleType)
{
    for (TSubclassOf<ABaseCollectible> CollectibleClass : LoadedCollectibleAssets)
    {
        if (CollectibleClass)
        {
            // Get the default object to access its properties
            ABaseCollectible* DefaultCollectible = CollectibleClass->GetDefaultObject<ABaseCollectible>();
            if (DefaultCollectible)
            {
                if (CollectibleType != ECollectibleType::None && DefaultCollectible->GetCollectibleType() == CollectibleType)
                {
                    return CollectibleClass;
                }
            }
        }
    }
    return nullptr;
}


void ASpawnManager::LoadCollectibleAssets()
{
    LoadedCollectibleAssets.Empty();

    if (!CollectibleDataTable)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: PlatformCollectibleDataTable is not set"));
        return;
    }

    // Get all rows from the data table
    const TMap<FName, uint8*>& RowMap = CollectibleDataTable->GetRowMap();

    // Verify the row struct type
    if (CollectibleDataTable->GetRowStruct()->IsChildOf(FCollectibleAssetData::StaticStruct()))
    {
        for (const TPair<FName, uint8*>& Row : RowMap)
        {
            FCollectibleAssetData* CollectibleData = CollectibleDataTable->FindRow<FCollectibleAssetData>(Row.Key, TEXT(""));
            if (CollectibleData && CollectibleData->CollectibleClasses)
            {
                LoadedCollectibleAssets.Add(CollectibleData->CollectibleClasses);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("Invalid CollectibleData in row %s"), *Row.Key.ToString());
            }
        }
    }

    if (LoadedCollectibleAssets.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("SpawnManager: Loaded collectible assets are empty"));
    }
}

#pragma endregion
