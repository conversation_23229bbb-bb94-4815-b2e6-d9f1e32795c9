// Fill out your copyright notice in the Description page of Project Settings.


#include "F1ghterFlyPlayerController.h"
#include <F1ghterFlyCharacter.h>
#include <JetPlayerCameraManager.h>
#include <GameplayWidget.h>
#include "F1ghterFlyGameState.h"
#include "GameModes/F1ghterFlyGameMode.h"

AF1ghterFlyPlayerController::AF1ghterFlyPlayerController()
{
}

void AF1ghterFlyPlayerController::BeginPlay()
{
	Super::BeginPlay();

    if (GetNetMode() != NM_DedicatedServer) {
        // Subscribe to the OnJetImpact delegate of the controlled character
        if (AF1ghterFlyCharacter* F1ghterFlyCharacter = Cast<AF1ghterFlyCharacter>(GetCharacter()))
        {
            F1ghterFlyCharacter->OnJetImpact.AddDynamic(this, &AF1ghterFlyPlayerController::OnJetImpact);
        }

        EnableInput(this);
    }

}


void AF1ghterFlyPlayerController::ReceivedPlayer()
{
	Super::ReceivedPlayer();
    
}

void AF1ghterFlyPlayerController::OnJetImpact(EJetBodyPart BodyPart)
{
    if (AF1ghterFlyCharacter* F1ghterFlyCharacter = Cast<AF1ghterFlyCharacter>(GetCharacter()))
    {
        // Only play the camera shake on the client for the locally controlled character
        if (GetNetMode() != NM_DedicatedServer && F1ghterFlyCharacter->IsLocallyControlled())
        {

            if (AJetPlayerCameraManager* CameraManager = Cast<AJetPlayerCameraManager>(PlayerCameraManager))
            {
                TSubclassOf<UCameraShakeBase> ShakeClass = (BodyPart == EJetBodyPart::Wings) ? WingDamageShake : MainBodyDamageShake;
                if (ShakeClass)
                {
                    CameraManager->StartCameraShake(ShakeClass,1.0f, ECameraShakePlaySpace::World);
                }
            }
        }
    }
}

void AF1ghterFlyPlayerController::Server_RestartGame_Implementation()
{
    if (AF1ghterFlyGameMode* F1ghterFlyGameMode = Cast<AF1ghterFlyGameMode>(GetWorld()->GetAuthGameMode()))
    {
        F1ghterFlyGameMode->RestartGame();
    }
}

void AF1ghterFlyPlayerController::CreateGameWidget()
{
	if (GetNetMode() == NM_DedicatedServer) return;

    if (IsLocalController() && GameplayWidgetClass)
    {
        GameplayWidget = CreateWidget<UGameplayWidget>(this, GameplayWidgetClass);
        if (GameplayWidget)
        {
            GameplayWidget->AddToViewport();
        }
    }
}
