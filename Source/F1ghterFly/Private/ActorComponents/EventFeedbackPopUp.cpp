// Fill out your copyright notice in the Description page of Project Settings.


#include "ActorComponents/EventFeedbackPopUp.h"
#include "Kismet/GameplayStatics.h"

// Sets default values for this component's properties
UEventFeedbackPopUp::UEventFeedbackPopUp()
{
	// Set this component to be initialized when the game starts, and to be ticked every frame.  You can turn these features
	// off to improve performance if you don't need them.
	PrimaryComponentTick.bCanEverTick = false;
}

void UEventFeedbackPopUp::BeginPlay()
{
	Super::BeginPlay();

	// Get reference to the scene component that will be used to position the feedback widget
	if ( AActor* Actor = GetOwner())
	{
		if (USceneComponent* MySceneComp = Actor->FindComponentByTag<USceneComponent>(FName("FeedbackUIPosition")))
		{
			FeedbackWidgetSceneComponent = MySceneComp;
		} else { UE_LOG(LogTemp, Error, TEXT("UEventFeedbackPopUp: FeedbackUIPosition component not found"));}
	}

}

FVector2D UEventFeedbackPopUp::GetFeedbackWidgetScreenPosition()
{
	APlayerController* PC = GetWorld()->GetGameInstance()->GetFirstLocalPlayerController();
	if (!IsValid(PC)) { UE_LOG(LogTemp, Error, TEXT("UEventFeedbackPopUp: PLAYER CONTROLLER is null")); return FVector2D::ZeroVector; }
	
	FVector WorldLocation = FeedbackWidgetSceneComponent->GetComponentLocation();
	FVector2D ScreenPosition = FVector2D::ZeroVector;
	UGameplayStatics::ProjectWorldToScreen(PC, WorldLocation, ScreenPosition);
	return ScreenPosition;
}

APlayerController* UEventFeedbackPopUp::GetPlayerController() const
{
	return GetWorld()->GetGameInstance()->GetFirstLocalPlayerController();
}