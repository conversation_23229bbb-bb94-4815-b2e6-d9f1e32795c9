// Fill out your copyright notice in the Description page of Project Settings.
#include "F1ghterFlyGameState.h"
#include "Net/UnrealNetwork.h"
#include "GameModes/F1ghterFlyGameMode.h"
#include "SpawnManager.h"
#include "Kismet/GameplayStatics.h"
#include <F1ghterFly/Utils/PlatformUtils.h>
#include "Subsystems/PowerUpSystem.h"

AF1ghterFlyGameState::AF1ghterFlyGameState()
{
    // ENABLE TICK
    PrimaryActorTick.bCanEverTick = false; 
    
}

void AF1ghterFlyGameState::PostInitializeComponents() 
{

    Super::PostInitializeComponents();

    // Give Fast Array Serializer ActivePlatforms a reference to Game State
    ActivePlatforms.OwningGameState = this;

    // Find the SpawnManager in the world
    if (ASpawnManager* CurrentSpawnManager = Cast<ASpawnManager>(UGameplayStatics::GetActorOfClass(GetWorld(), ASpawnManager::StaticClass())))
    {
        // Call a function to handle initialization of assets and pool
        SpawnManager = CurrentSpawnManager;
    }
    else { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState->InitializeSeedDependantInstances: SpawnManager not found")); }


}

void AF1ghterFlyGameState::BeginPlay()
{
    Super::BeginPlay();
    PowerUpSystem = GetGameInstance()->GetSubsystem<UPowerUpSystem>();
    
    // Server / Standalone
    if (HasAuthority())
    {
         InitializeSeedDependantInstances(); // server version
    }
}

void AF1ghterFlyGameState::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);

}


void AF1ghterFlyGameState::SetSeed(int32 InSeed)
{
    CurrentSeed = InSeed;

    // If Standalone or Server, directly call on_Rep
    if (HasAuthority())
    {
        OnRep_Seed();
    }
}

void AF1ghterFlyGameState::OnRep_Seed()
{
    // Future logic maybe, where we change seed during gameplay ??
}

void AF1ghterFlyGameState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    // Ensure ForwardSpeed gets replicated across clients
    DOREPLIFETIME(AF1ghterFlyGameState, ForwardSpeed);
    // Replicate the Seed variable
    DOREPLIFETIME(AF1ghterFlyGameState, CurrentSeed);
	// Replicate the ActivePlatforms array
    DOREPLIFETIME(AF1ghterFlyGameState, ActivePlatforms);
}

void AF1ghterFlyGameState::OnRep_ForwardSpeed()
{
    // Notify listeners that ForwardSpeed has changed
    OnForwardSpeedChanged.Broadcast();
}

void AF1ghterFlyGameState::BroadcastNextPlatformToActivate(int32 PlatformSequenceNumber)
{
    OnActivateNextPlatforms.Broadcast(PlatformSequenceNumber);
}

void AF1ghterFlyGameState::IncreaseForwardSpeed()
{
    if (HasAuthority()) // Only the server should modify this
    {
       ForwardSpeed += 1000.0f; // Increment speed
       ForwardSpeed = FMath::Clamp(ForwardSpeed,4000, 20000);
        
        OnRep_ForwardSpeed();
    }
}

void AF1ghterFlyGameState::BeginDestroy()
{
    Super::BeginDestroy();
    //UE_LOG(LogTemp, Error, TEXT("GameState is being destroyed!"));
}

// Spawns the initial platform based on the sequence number
void AF1ghterFlyGameState::SpawnInitialPlatform()
{
    //Server/Client: Allow event client to spawn the initial platform. Does not depend on replication to spawn for the first platform.
    if (IsValid(SpawnManager)) {
        
        if (AEnvPlatform* InitialPlatform = SpawnManager->SpawnInitialPlatform()) {
            
            // Server / Standalone: Update the ActivePlatforms array
            if (HasAuthority())
            {
                ActivePlatforms.AddPlatform(
                    InitialPlatform->GetSequenceNumber(),
                    InitialPlatform->GetActorLocation()
                );    
            }
        }
    }
}

void AF1ghterFlyGameState::HandlePlatformSpawnRequest(FInputRequestSpawn Input)
{
    if (!IsValid(SpawnManager)) { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState: SpawnManager not found ")); return; };

    TArray<AEnvPlatform*> ActivePlatformPool = FPlatformUtils::GetAllActivePlatforms(SpawnManager->GetPlatformPool());

    // Find the Max Sequence Number
    int32 MaxSequenceNumber = 1;
    for (AEnvPlatform* Platform : ActivePlatformPool)
    {
        MaxSequenceNumber = FMath::Max(MaxSequenceNumber, Platform->GetSequenceNumber());
    }

    int16 SpawnAmount = 2; // Queue 2 platforms per request
    for (int16 i = 1; i <=SpawnAmount; i++) {
        // if sequence number already in queue, don't queue !!
        if (SpawnManager->GetQueuedSequenceNumbers().Contains(MaxSequenceNumber + i)) continue;
        
        SpawnManager->QueueActivePlatformSpawn(MaxSequenceNumber + i);
    }

}


void AF1ghterFlyGameState::OnServerReturnPlatformToPool(int16 SequenceNumber)
{
    if (!HasAuthority()) return;

    if (!IsValid(SpawnManager)) { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState: HandleDeactivatingPlatform: sSpawnManager not found / cast properly")); return; };

    SpawnManager->ReturnPlatformToPoolBySequenceNumber(SequenceNumber);

    // Clean up the tracker list after removal.
    ActivePlatforms.RemovePlatformBySequence(SequenceNumber);
}

void AF1ghterFlyGameState::InitializeSeedDependantInstances()
{

    // Get PowerUpSystem from GameInstance and Initialize the PowerUpSystem with the PowerUpSpawnChancesDataTable and the Seed
    if (PowerUpSystem && PowerUpSpawnChancesDataTable)
    {
        PowerUpSystem->InitializePowerUpSystem(PowerUpSpawnChancesDataTable);
    }
    else { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState->InitializeSeedDependantInstances: PowerUpSystem is null"));}
 
    // Find the SpawnManager in the world
    if (ASpawnManager* CurrentSpawnManager = Cast<ASpawnManager>(UGameplayStatics::GetActorOfClass(GetWorld(), ASpawnManager::StaticClass())))
    {
        // Call a function to handle initialization of assets and pool
        SpawnManager->StartInitializingAllPools();
    }
    else { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameState->InitializeSeedDependantInstances: SpawnManager not found")); }

    // Spawn the initial platform
    SpawnInitialPlatform();
}