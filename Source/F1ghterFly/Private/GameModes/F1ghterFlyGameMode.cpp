// Copyright Epic Games, Inc. All Rights Reserved.

#include "GameModes/F1ghterFlyGameMode.h"
#include "F1JetHud.h"
#include "F1ghterFlyGameState.h"
#include "F1ghterFly/Types/PlatformTypes.h"

AF1ghterFlyGameMode::AF1ghterFlyGameMode()
{
	if (DefaultPawnBPClass)
	{
		DefaultPawnClass = DefaultPawnBPClass;
	}
	HUDClass = AF1JetHud::StaticClass(); // Set the default HUD class
	GameStateClass = AF1ghterFlyGameState::StaticClass(); // set the default game state class
}

void AF1ghterFlyGameMode::BeginPlay()
{
	Super::BeginPlay();

	
	Seed = FMath::Rand();

	if (AF1ghterFlyGameState* F1ghterFlyGameState = GetGameState<AF1ghterFlyGameState>())
	{
		F1ghterFlyGameState->SetSeed(Seed);
	}
	else { UE_LOG(LogTemp, Warning, TEXT("Server: GameState not ready in GameMode BeginPlay"));}
	
	// Start the timer to increase speed every 5 seconds
	GetWorldTimerManager().SetTimer(SpeedIncreaseTimer, this, &AF1ghterFlyGameMode::IncreaseGameSpeed, 30.0f, true);
}

void AF1ghterFlyGameMode::PostLogin(APlayerController* NewPlayer)
{
	Super::PostLogin(NewPlayer);

	// Initialize platform state for new player
	if (NewPlayer && !AllPlayersPlatformState.Contains(NewPlayer))
	{
		AllPlayersPlatformState.Add(NewPlayer, { 1, 0 });
		UE_LOG(LogTemp, Log, TEXT("Platform state initialized for player: %s"), *NewPlayer->GetName());

		// Spawn and possess the default pawn for the new player
		SpawnAndPossessDefaultPawn(NewPlayer);

		
	}
}

void AF1ghterFlyGameMode::SpawnAndPossessDefaultPawn(APlayerController* NewPlayer)
{
	if (!NewPlayer || !DefaultPawnBPClass)
	{
		UE_LOG(LogTemp, Warning, TEXT("PostLogin: Invalid PlayerController or Pawn class"));
		return;
	}

	AActor* StartSpot = FindPlayerStart(NewPlayer);
	if (!StartSpot)
	{
		UE_LOG(LogTemp, Error, TEXT("SpawnAndPossessDefaultPawn: No PlayerStart found"));
		return;
	}

	FTransform SpawnTransform = StartSpot->GetActorTransform();
	FActorSpawnParameters SpawnParams;
	SpawnParams.Owner = NewPlayer;

	// will have logic later to get jet type from player game instance. For now, just using default

	
	APawn* NewPawn = GetWorld()->SpawnActor<APawn>(DefaultPawnBPClass, SpawnTransform, SpawnParams);
	if (!NewPawn)
	{
		UE_LOG(LogTemp, Error, TEXT("SpawnAndPossessDefaultPawn: Failed to spawn pawn"));
		return;
	}

	NewPlayer->Possess(NewPawn);
}

void AF1ghterFlyGameMode::Restart()
{
	RestartGame();
}

void AF1ghterFlyGameMode::Server_StartGame_Implementation()
{
	/*AF1ghterFlyGameState* GameState = GetGameState<AF1ghterFlyGameState>();
	if (!GameState) return;*/

	// Generate new seed
	//GameState->WorldSeed = FMath::Rand();

	// Set theme (could be random or from settings)
	//CurrentTheme = "Desert"; // Example
	//if (ASpawnManager* SpawnManager = GetSpawnManager())
	//{
	//	SpawnManager->InitializeForTheme(CurrentTheme);
	//}

	// Spawn players
	// SpawnPlayers();

	// Load first platform
	// SpawnInitialPlatform();

	// Notify clients to start intro
	// Multicast_BeginIntro();
}

bool AF1ghterFlyGameMode::IsRequestValid(AController* PC, int16 SequenceNum, float TriggerPosX)
{
	return true;

	//FPlayerCurrentPlatformState* PlayerCurrentPlatformState = AllPlayersPlatformState.Find(PC);
	//if (!PlayerCurrentPlatformState) return false;

	////UE_LOG(LogTemp, Warning, TEXT("Logging IS REQUEST VALID ,SequenceNum = %d  , NextValidSequenceNumber = %d, LastValidatedSequenceNumber = %d "), SequenceNum , PlayerCurrentPlatformState->NextValidSpawnSequenceNumber, PlayerCurrentPlatformState->LastValidatedSequenceNumber);
	//// Sequence Number client send should be the current sequence number for that player
	//if (SequenceNum > PlayerCurrentPlatformState->NextValidSpawnSequenceNumber || SequenceNum < PlayerCurrentPlatformState->LastValidatedSequenceNumber) return false;

	//// Check position against trigger
	//FVector PlayerPosition = PC->GetPawn()->GetActorLocation();
	////UE_LOG(LogTemp, Warning, TEXT("GameMode: Player position X: %f  , TriggerPosX: %f"), PlayerPosition.X, TriggerPosX);

	//return (TriggerPosX - PlayerPosition.X) < 300; // 300 is a guess number. I'm saying if jet is 300units from the trigger, consider it valid

}

void AF1ghterFlyGameMode::HandlePlatformSpawnRequest(FInputRequestSpawn Input)
{

	if (IsRequestValid(Input.PC, Input.SequenceNum, Input.TriggerPosX))
	{
		// get game state 
		AF1ghterFlyGameState* F1ghterFlyGameState = GetGameState<AF1ghterFlyGameState>();

		if (!F1ghterFlyGameState) { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameMode: F1ghterFlyGameState not found / cast properly")); return; };

		F1ghterFlyGameState->HandlePlatformSpawnRequest(Input);

	}
}

void AF1ghterFlyGameMode::HandleDeactivatingPlatform(AEnvPlatform* Platform)
{
	// get game state 
	AF1ghterFlyGameState* F1ghterFlyGameState = GetGameState<AF1ghterFlyGameState>();

	if (!IsValid(F1ghterFlyGameState)) { UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyGameMode: F1ghterFlyGameState not found / cast propertly")); return; };

	// We need to make sure we only deactivate a platforms if all players have passed the platform.
	TArray<float> ActorsLocationX;
	ActorsLocationX.Reserve(AllPlayersPlatformState.Num());

	Algo::Transform(AllPlayersPlatformState, ActorsLocationX, [](const auto& Entry) -> float
	{
		AController* Controller = Entry.Key.Get();
		return (Controller && Controller->GetPawn()) ? Controller->GetPawn()->GetActorLocation().X : 0.0f;
	});

	if (ActorsLocationX.Num() > 0) // Ensure there are players in the game
	{
		float MinPlayerX = FMath::Min(ActorsLocationX); // Get the last player's position
		TArray<int16> SequenceToDelete;

		for (const FActivePlatformData& ActivePlatform : F1ghterFlyGameState->ActivePlatforms.Items)
		{
			// If a platform is behind the last player, subject it to be deactivated!
			if (ActivePlatform.SpawnPosition.X < MinPlayerX)
			{
				SequenceToDelete.Add(ActivePlatform.SequenceNumber);
			}
		}

		// if there are platforms to "return to pool"
		if (SequenceToDelete.Num() > 0) {
			for (const int16& SequenceNumber : SequenceToDelete)
			{
				F1ghterFlyGameState->OnServerReturnPlatformToPool(SequenceNumber);
			}

		}

	}

}

void AF1ghterFlyGameMode::IncreaseGameSpeed()
{
	AF1ghterFlyGameState* F1ghterFlyGameState = GetGameState<AF1ghterFlyGameState>();
	if (F1ghterFlyGameState && HasAuthority()) // Make sure it's server-side
	{
		F1ghterFlyGameState->IncreaseForwardSpeed(); // Call GameState function to update speed
	}
}