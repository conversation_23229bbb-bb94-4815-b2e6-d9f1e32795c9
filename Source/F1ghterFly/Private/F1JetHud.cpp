#include "F1JetHud.h"
#include "Engine/Canvas.h"
#include "Engine/Font.h"
#include "GameFramework/HUD.h"
#include "BaseCollectible.h"
#include <Kismet/GameplayStatics.h>
#include <F1ghterFlyPlayerState.h>
#include <F1ghterFlyPlayerController.h>
#include <ActorComponents/EffectManager.h>

#include "ActorComponents/JetEventManager.h"


AF1JetHud::AF1JetHud()
{
}

void AF1JetHud::BeginPlay()
{
    //GetWorld()->GetTimerManager().SetTimer(RetryBindTimerHandle, this, &AF1JetHud::SetupHub, 1.0f, false);

    // Set a timer to periodically update collectible info (e.g., every 0.5 seconds)
    GetWorld()->GetTimerManager().SetTimerForNextTick(this, &AF1JetHud::UpdateCollectibleInfo);
}

void AF1JetHud::DrawHUD()
{
    if (Canvas)
    {
        FLinearColor TextColor;
        if (CurrentDistance > 1000000) TextColor = FLinearColor::Red;
        else if (CurrentDistance > 10000) TextColor = FLinearColor::Green; // Flag 10,000
        else TextColor = FLinearColor::White;

        FString DistanceText = FString::Printf(TEXT("Distance: %.0f cm"), CurrentDistance);
        FString JetSpeedText = FString::Printf(TEXT("Speed: %.0f"), JetSpeed);
        FString HealthText = FString::Printf(TEXT("Health: %.0f"), Health);

        FString CollectiblesText = FString::Printf(TEXT("Collectibles: %f"), TotalCollectibles);
        FString ActiveCollectiblesText = FString::Printf(TEXT("Active: %f"), ActiveCollectibles);

        if (CurrentDistance > 1000000)
        {
            DistanceText += " - EXCEEDED DISTANCE!";
        }

        // Draw text on the top-right corner
        float X = Canvas->SizeX - 300; // Right side
        float Y = 50; // Top of the screen

        // distance travel
        FCanvasTextItem TextItem(FVector2D(X, Y), FText::FromString(DistanceText), GEngine->GetMediumFont(), TextColor);

        // jet speed
        FCanvasTextItem TextItem_JetSpeed(FVector2D(X, 80), FText::FromString(JetSpeedText), GEngine->GetMediumFont(), FLinearColor::Yellow);

        // health
        FCanvasTextItem TextItem_Health(FVector2D(X, 110), FText::FromString(HealthText), GEngine->GetMediumFont(), FLinearColor::White);


        // Collectibles
        FCanvasTextItem TextItem_Collectibles(FVector2D(X, 140), FText::FromString(CollectiblesText), GEngine->GetMediumFont(), FLinearColor::White);

        // Active Collectibles
        FCanvasTextItem TextItem_ActiveCollectibles(FVector2D(X, 170), FText::FromString(ActiveCollectiblesText), GEngine->GetMediumFont(), FLinearColor::Green);


        Canvas->DrawItem(TextItem);
        Canvas->DrawItem(TextItem_JetSpeed);
        Canvas->DrawItem(TextItem_Health);

        Canvas->DrawItem(TextItem_Collectibles);
        Canvas->DrawItem(TextItem_ActiveCollectibles);


		// Draw power-up slots
		UpdatePowerUpSlotsUI();
    }
}

void AF1JetHud::SetupHub()
{

    if (GetNetMode() != NM_DedicatedServer) {
        if (AF1ghterFlyPlayerController* PC = Cast<AF1ghterFlyPlayerController>(GetOwningPlayerController()))
        {
            
            // Subscribe to health change
            if (AF1ghterFlyCharacter* JetCharacter = Cast<AF1ghterFlyCharacter>(PC->GetCharacter())) {
                 JetCharacter->GetJetEventManager()->OnHealthChanged.AddDynamic(this, &AF1JetHud::UpdateHealthUI);
            }
            
        }
    }


}

void AF1JetHud::UpdateTravelDistance(float Distance)
{
    CurrentDistance = Distance;
}

void AF1JetHud::UpdateHealthUI(float NewHealth)
{
    Health = NewHealth;
}

void AF1JetHud::UpdateCollectibleInfo() {
    if (GetNetMode() != NM_DedicatedServer) { // Find all collectibles in the world TArray<AActor*> FoundCollectibles; UGameplayStatics::GetAllActorsOfClass(GetWorld(), ACollectible::StaticClass(), FoundCollectibles);
        TArray<AActor*> FoundCollectibles;

        UGameplayStatics::GetAllActorsOfClassWithTag(GetWorld(), ABaseCollectible::StaticClass(), FName("Collectible"), FoundCollectibles);

        TotalCollectibles = FoundCollectibles.Num();
        ActiveCollectibles = 0;

        // Count active collectibles
        for (AActor* Actor : FoundCollectibles)
        {
            if (ABaseCollectible* Collectible = Cast<ABaseCollectible>(Actor))
            {
                if (Collectible->IsHidden() == false)
                {
                    ActiveCollectibles++;
                }
            }
        }

        // Schedule the next update
        GetWorld()->GetTimerManager().SetTimerForNextTick(this, &AF1JetHud::UpdateCollectibleInfo);
    }
}

void AF1JetHud::UpdatePowerUpSlotsUI() {
    if (GetNetMode() != NM_DedicatedServer) {
        if (AF1ghterFlyPlayerController* PC = Cast<AF1ghterFlyPlayerController>(GetOwningPlayerController()))
        {
            if (Canvas && PC->GetCharacter()) {

				UEffectManager* EffectManager = PC->GetCharacter()->FindComponentByClass<UEffectManager>();

				if (!EffectManager) {
					return; // No EffectManager found
				}

                float X = 300; // Left side
                float Y = 50; // Top of the screen

                if (EffectManager->PowerUpSlots.Num() < 1) {
                    FCanvasTextItem TextItem(FVector2D(X, Y), FText::FromString("No powerup equip in slots"), GEngine->GetMediumFont(), FLinearColor::White);
                    Canvas->DrawItem(TextItem);
                }


                for (FPowerUpPickUp PickUp : EffectManager->PowerUpSlots) {
                    FString PowerUpText = FString::Printf(TEXT("PowerUp: %s"), *UEnum::GetValueAsString(PickUp.PowerUpType));
                    FCanvasTextItem TextItem(FVector2D(X, Y), FText::FromString(PowerUpText), GEngine->GetMediumFont(), FLinearColor::Blue);
                    Canvas->DrawItem(TextItem);

                    Y += 30; // Move down for the next power-up
                }
            }
        }
    }
}