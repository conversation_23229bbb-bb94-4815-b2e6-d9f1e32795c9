// Copyright Epic Games, Inc. All Rights Reserved.

#include "F1ghterFlyCharacter.h"
#include "Engine/LocalPlayer.h"
#include "Camera/CameraComponent.h"
#include "Components/CapsuleComponent.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Controller.h"
#include "EnhancedInputComponent.h"
#include "EnhancedInputSubsystems.h"
#include "InputActionValue.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "F1JetHud.h"
#include "GameFramework/Character.h"
#include "Net/UnrealNetwork.h"
#include "JetCharacterMovementComponent.h"
#include "GameModes/F1ghterFlyGameMode.h"
#include <F1ghterFlyPlayerState.h>
#include "Components/BoxComponent.h"
#include <ActorComponents/EffectManager.h>
#include "Components/ActorComponent.h"     // general components
#include <CommonUtils.h>
#include "Components/AudioComponent.h"
#include "ActorComponents/ScoreManager.h"
#include <F1ghterFlyPlayerController.h>
#include <GameplayWidget.h>
#include "ActorComponents/JetEventManager.h"


// Helper Macros
//#if 0
//float MacroDuration = 2.f;
//#define SLOG(x) GEngine->AddOnScreenDebugMessage(-1, MacroDuration ? MacroDuration : -1.f, FColor::Yellow, x);
//#define PRINT(fmt, ...) \
//	if (GEngine) \
//		GEngine->AddOnScreenDebugMessage(-1, MacroDuration ? MacroDuration : -1.f, FColor::Yellow, FString::Printf(fmt, ##__VA_ARGS__));
//#define POINT(x, c) DrawDebugPoint(GetWorld(), x, 10, c, !MacroDuration, MacroDuration);
//#define LINE(x1, x2, c) DrawDebugLine(GetWorld(), x1, x2, c, !MacroDuration, MacroDuration);
//#define CAPSULE(x, c) DrawDebugCapsule(GetWorld(), x, CapHH(), CapR(), FQuat::Identity, c, !MacroDuration, MacroDuration);
//#else
//#define SLOG(x)
//#define POINT(x, c)
//#define LINE(x1, x2, c)
//#define CAPSULE(x, c)
//#endif


DEFINE_LOG_CATEGORY(LogTemplateCharacter);

//////////////////////////////////////////////////////////////////////////
// AF1ghterFlyCharacter

AF1ghterFlyCharacter::AF1ghterFlyCharacter(const FObjectInitializer& ObjectInitializer)
	:Super(ObjectInitializer.SetDefaultSubobjectClass<UJetCharacterMovementComponent>(ACharacter::CharacterMovementComponentName))
{
	

	// Set size for collision capsule
	GetCapsuleComponent()->InitCapsuleSize(22.0f, 22.0f);

	// Don't rotate when the controller rotates. Let that just affect the camera.
	bUseControllerRotationPitch = false;
	bUseControllerRotationYaw = false;
	bUseControllerRotationRoll = false;
	// Configure character movement
	GetCharacterMovement()->bOrientRotationToMovement = false; // Character moves in the direction of input...	
	GetCharacterMovement()->bUseControllerDesiredRotation = false;

	GetCharacterMovement()->RotationRate = FRotator(0.0f, 0.0f, 0.0f); // ...at this rotation rate
	GetCharacterMovement()->BrakingFrictionFactor = 0.0f;
	GetCharacterMovement()->BrakingFriction = 0.0f;
	GetCharacterMovement()->bUseSeparateBrakingFriction = false;
	GetCharacterMovement()->bForceMaxAccel = true;

	// Note: For faster iteration times these variables, and many more, can be tweaked in the Character Blueprint
	// instead of recompiling to adjust them
	GetCharacterMovement()->JumpZVelocity = 700.f;
	GetCharacterMovement()->AirControl = 0.35f;
	GetCharacterMovement()->MaxWalkSpeed = 500.f;
	GetCharacterMovement()->MinAnalogWalkSpeed = 20.f;
	GetCharacterMovement()->BrakingDecelerationWalking = 2000.f;
	GetCharacterMovement()->BrakingDecelerationFalling = 1500.0f;

	// Create a camera boom (pulls in towards the player if there is a collision)
	CameraBoom = CreateDefaultSubobject<USpringArmComponent>(TEXT("CameraBoom"));
	CameraBoom->bUsePawnControlRotation = false; // Rotate the arm based on the controller

	CameraBoom->SetupAttachment(RootComponent);
	//CameraBoom->TargetArmLength = 400.0f; // The camera follows at this distance behind the character	

	JetEffectManager = CreateDefaultSubobject<UEffectManager>(TEXT("EffectManager"));
	ScoreManager = CreateDefaultSubobject<UScoreManager>(TEXT("ScoreManager"));
	CollectionAudioComponent = CreateDefaultSubobject<UAudioComponent>(TEXT("CollectionAudio"));
	CollectionAudioComponent->bAutoActivate = false;
	
	FeedbackWidgetSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("FeedbackWidgetPosition"));
	FeedbackWidgetSceneComponent->SetupAttachment(GetRootComponent());
	
	// Niagara Component setup. Default is 2 per jet. Increase if needs be
	for (int32 i = 1; i <= 2; i++)
	{
		UNiagaraComponent* Comp = CreateDefaultSubobject<UNiagaraComponent>(*FString::Printf(TEXT("VFXComponent%d"), i));
		Comp->SetupAttachment(GetMesh());
		Comp->SetAutoActivate(false);
		VFXComponents.Add(Comp);
	}

	// Low Health Niagara Component
	LowHealthVFXComponent = CreateDefaultSubobject<UNiagaraComponent>(TEXT("LowHealthVFXComponent"));
	LowHealthVFXComponent->SetupAttachment(GetMesh());
	LowHealthVFXComponent->SetAutoActivate(false);
	LowHealthVFXComponent->ComponentTags.Add(FName("LowHealthVFX"));

	// Create a follow camera
	FollowCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("FollowCamera"));
	FollowCamera->bUsePawnControlRotation = false; // Camera does not rotate relative to arm

	FollowCamera->SetupAttachment(CameraBoom, USpringArmComponent::SocketName); // Attach the camera to the end of the boom and let the boom adjust to match the controller orientation

	// Top-down camera (same boom, or create separate boom if needed)
	TopDownCamera = CreateDefaultSubobject<UCameraComponent>(TEXT("TopDownCamera"));
	TopDownCamera->SetupAttachment(CameraBoom);
	TopDownCamera->SetRelativeLocation(FVector(0.f, 0.f, 1000.f));  // Place above
	TopDownCamera->SetRelativeRotation(FRotator(-90.f, 0.f, 0.f)); // Look straight down

	// Note: The skeletal mesh and anim blueprint references on the Mesh component (inherited from Character) 
	// are set in the derived blueprint asset named ThirdPersonCharacter (to avoid direct content references in C++)

	LeftWingCollider = CreateDefaultSubobject<UBoxComponent>(TEXT("WingColliderLeft"));
	RightWingCollider = CreateDefaultSubobject<UBoxComponent>(TEXT("WingColliderRight"));

	LeftWingCollider->SetupAttachment(GetMesh());
	RightWingCollider->SetupAttachment(GetMesh());

	// Set up the collider to detect collectible items
	CollectibleCollider = CreateDefaultSubobject<UBoxComponent>(TEXT("CollectibleCollider"));
	CollectibleCollider->SetBoxExtent(FVector(80.0f, 50.0f, 20.0f));
	CollectibleCollider->SetCollisionProfileName(TEXT("CollectibleCollider"));
	CollectibleCollider->SetupAttachment(RootComponent);


	bIsMoving = false;
	bIsDashing = false;

	TouchSensitivity = 0.2f; // Base sensitivity (will be scaled by DPI)
	TouchSmoothingFactor = 0.2f; // Light smoothing to reduce jitter without lag
	TouchDeadZone = 0.05f; // Small dead zone to ignore tiny inputs


	PreviousMoveTouchPosition = FVector2D::ZeroVector;
	DashInitialTouchPosition = FVector2D::ZeroVector;

	MovementTouchIndex = -1;
	DashTouchIndex = -1;

	DashSwipeDistance = 50.0f; // Minimum swipe distance to trigger a dash (in pixels)

}

void AF1ghterFlyCharacter::PostInitializeComponents()
{

	 Super::PostInitializeComponents();
	

}

void AF1ghterFlyCharacter::BeginDestroy()
{
	Super::BeginDestroy();
	UE_LOG(LogTemp, Log, TEXT("Jet Destroyed: %s , netmode: %d , hasAuthority: %s"),
		*GetName(),
		static_cast<int32>(GetNetMode()),
		HasAuthority() ? TEXT("TRUE") : TEXT("FALSE"));
}

void AF1ghterFlyCharacter::BeginPlay()
{
	Super::BeginPlay();
	
	// get the jet event manager actor component
	JetEventManager = FindComponentByTag<UJetEventManager>(TEXT("AC_JetEventManager"));
	if (!IsValid(JetEventManager))
	{
		UE_LOG(LogTemp, Error, TEXT("AF1ghterFlyCharacter: JetEventManager is null"));
	}
	
	
	JetCharacterMovementComponent = Cast<UJetCharacterMovementComponent>(GetCharacterMovement());

	// Get the movement component (cast as your custom movement component)
	if (JetCharacterMovementComponent)
	{
		// Subscribe to the DashStartDelegate event
		JetCharacterMovementComponent->DashStartDelegate.AddDynamic(this, &AF1ghterFlyCharacter::OnDashStart);

		// Server subscription
		if (HasAuthority()) {
			// Subscribe to the DashStartDelegate event
			JetEffectManager->OnJetEffectModeChanged.AddDynamic(this, &AF1ghterFlyCharacter::SubscribeTo_OnJetEffectModeChanged);
		}
	}

	// Get reference to GameState
	if (AF1ghterFlyGameState* GameState = GetWorld()->GetGameState<AF1ghterFlyGameState>())
	{
		GameState->OnForwardSpeedChanged.AddUObject(this, &AF1ghterFlyCharacter::SubscribeTo_ForwardSpeedChanged);

		// Initial Speed Given by game state
		JetForwardSpeed = GameState->ForwardSpeed;
	}

	// Call PlayerState OnRep if Standalone
	if (GetNetMode() == NM_Standalone) {
		OnRep_PlayerState();
	}

	// broadcast low health. This needs to be move somewhere else. any health logic and "damage taken" logic in this file, need to be in a better place. :(
	// When other jets get replicated again and appear in other players view.
	if (AF1ghterFlyPlayerState* F1ghterFlyPlayerState = Cast<AF1ghterFlyPlayerState>(GetPlayerState()))
	{
		if (IsValid(JetEventManager))
		{
			if ((F1ghterFlyPlayerState->GetHealth() / F1ghterFlyPlayerState->GetMaxHealth()) < 0.5f) {
				GetJetEventManager()->BroadcastLowHealth(F1ghterFlyPlayerState->GetHealth());
			}
		}
	}
	
}

void AF1ghterFlyCharacter::OnRep_PlayerState()
{
	Super::OnRep_PlayerState();

	// Initialize Gameplay widget
	if (IsLocallyControlled()) {
		if(AF1ghterFlyPlayerController* F1ghterFlyController = Cast<AF1ghterFlyPlayerController>(GetController())){

			// Create widget
			F1ghterFlyController->CreateGameWidget();

			// Initialize the GameplayWidget if it exists
			if (UGameplayWidget* GameplayWidget = F1ghterFlyController->GetGameplayWidget())
			{
				GameplayWidget->InitializeWidget(Cast<AF1ghterFlyPlayerState>(GetPlayerState()));
			}
		}
	}
}

void AF1ghterFlyCharacter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Client/Standalone only logic
	if (GetNetMode() != NM_DedicatedServer && IsLocallyControlled()) {
		// Get distance from world origin
		float DistanceTraveled = GetActorLocation().X; // Assuming forward movement is in X direction

		// Update the HUD
		AF1JetHud* JetHUD = Cast<AF1JetHud>(GetWorld()->GetFirstPlayerController()->GetHUD());
		if (JetHUD)
		{
			JetHUD->UpdateTravelDistance(DistanceTraveled);
			if (JetCharacterMovementComponent) {
				JetHUD->JetSpeed = JetCharacterMovementComponent->Velocity.X;

			}
		}
	}
}

void AF1ghterFlyCharacter::SwitchCamera()
{
	if (!Controller) return;

	AF1ghterFlyPlayerController* AF1ghterFlyController = Cast<AF1ghterFlyPlayerController>(Controller);
	if (AF1ghterFlyController)
	{
		bool bUsingMain = FollowCamera->IsActive();

		if (bUsingMain)
		{
			FollowCamera->SetActive(false);
			TopDownCamera->SetActive(true);
		}
		else
		{
			FollowCamera->SetActive(true);
			TopDownCamera->SetActive(false);
		}

		// Ensure the player controller uses the correct view
		AF1ghterFlyController->SetViewTargetWithBlend(this, 0.3f); // Optional: smooth transition
	}
}

//////////////////////////////////////////////////////////////////////////
// Input

void AF1ghterFlyCharacter::NotifyControllerChanged()
{
	Super::NotifyControllerChanged();

	// Add Input Mapping Context
	if (APlayerController* PlayerController = Cast<APlayerController>(Controller))
	{
		if (UEnhancedInputLocalPlayerSubsystem* Subsystem = ULocalPlayer::GetSubsystem<UEnhancedInputLocalPlayerSubsystem>(PlayerController->GetLocalPlayer()))
		{
			Subsystem->AddMappingContext(DefaultMappingContext, 0);
		}
	}
}

void AF1ghterFlyCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	// Set up action bindings
	if (UEnhancedInputComponent* EnhancedInputComponent = Cast<UEnhancedInputComponent>(PlayerInputComponent)) {
		
		// Moving
		EnhancedInputComponent->BindAction(MoveAction, ETriggerEvent::Triggered, this, &AF1ghterFlyCharacter::Move);

		// Looking
		EnhancedInputComponent->BindAction(LookAction, ETriggerEvent::Triggered, this, &AF1ghterFlyCharacter::Look);

		// Bind Input Actions
		EnhancedInputComponent->BindAction(DashAction, ETriggerEvent::Triggered, this, &AF1ghterFlyCharacter::Dash);

	}
	else
	{
		UE_LOG(LogTemplateCharacter, Error, TEXT("'%s' Failed to find an Enhanced Input component! This template is built to use the Enhanced Input system. If you intend to use the legacy system, then you will need to update this C++ file."), *GetNameSafe(this));
	}

	PlayerInputComponent->BindKey(EKeys::Tab, IE_Pressed, this, &AF1ghterFlyCharacter::SwitchCamera);

}


void AF1ghterFlyCharacter::StartMovementTouch(FVector2D TouchPosition, int32 TouchIndex)
{

	// Check if it is already moving or if the touch index matches
	if ((bIsMoving && MovementTouchIndex != -1) || (MovementTouchIndex != -1 && TouchIndex != MovementTouchIndex))
	{
		return;
	}

	FVector2D ViewportSize;
	if (GEngine && GEngine->GameViewport)
	{
		GEngine->GameViewport->GetViewportSize(ViewportSize);
	}
	else
	{
		ViewportSize = FVector2D(1440.0f, 2960.0f);
	}


	FVector2D NormalizedTouchPosition;
	NormalizedTouchPosition.X = TouchPosition.X / ViewportSize.X;
	NormalizedTouchPosition.Y = TouchPosition.Y / ViewportSize.Y;

	if (NormalizedTouchPosition.X >= 0.5f && MovementTouchIndex == -1)
	{
		PreviousMoveTouchPosition = TouchPosition;
		MovementTouchIndex = TouchIndex;
		bIsMoving = true;
		return;
	}

	bIsMoving = false;
}

// Implement movement logic
void AF1ghterFlyCharacter::HandleMobileMove(FVector2D TouchPosition, int32 TouchIndex)
{
	// Check if the movement touch is active and if the touch index matches
	if (!bIsMoving || MovementTouchIndex == -1 || (MovementTouchIndex != -1 && TouchIndex != MovementTouchIndex))
	{
		return;
	}

	FVector2D CurrentTouchPosition = TouchPosition;

	// Lightweight smoothing using a simple moving average
	FVector2D SmoothedCurrentTouchPosition = FMath::Lerp(PreviousMoveTouchPosition, CurrentTouchPosition, TouchSmoothingFactor);

	// Calculate frame-to-frame delta
	FVector2D TouchDelta = SmoothedCurrentTouchPosition - PreviousMoveTouchPosition;
	PreviousMoveTouchPosition = SmoothedCurrentTouchPosition;

	// Dynamic delta threshold based on the magnitude of the delta
	// Adjusts the threshold based on the magnitude of TouchDelta. Small movements (e.g., 1 pixel) use a higher threshold (2.0 pixels) to filter noise; larger movements (e.g., 10 pixels) use a lower threshold (0.5 pixels) for responsiveness.
	// Reduces jitter during slow movements and ensures fast direction changes are not delayed
	float DeltaSpeed = TouchDelta.Size();
	float DynamicThreshold = FMath::Lerp(1.0f, 0.5f, DeltaSpeed / 10.0f); // Higher threshold for small movements
	if (TouchDelta.Size() < DynamicThreshold)
	{
		TouchDelta = FVector2D::ZeroVector;
	}

	// Convert delta to LookAxisVector
	FVector2D LookAxisVector;
	LookAxisVector.X = TouchDelta.X * TouchSensitivity;
	LookAxisVector.Y = (TouchDelta.Y * -1) * TouchSensitivity;

	// Apply dead zone
	// Ignores small inputs (e.g., LookAxisVector.Size() < 0.05) to prevent unintended movement from noise or thumb tremor.
	if (LookAxisVector.Size() < TouchDeadZone)
	{
		LookAxisVector = FVector2D::ZeroVector;
	}

	if (APawn* ControlledPawn = Controller->GetPawn())
	{
		// Find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FVector TopDirection = FRotationMatrix(Rotation).GetUnitAxis(EAxis::Z);
		const FVector RightDirection = FRotationMatrix(Rotation).GetUnitAxis(EAxis::Y);

		AddMovementInput(TopDirection, LookAxisVector.Y);
		AddMovementInput(RightDirection, LookAxisVector.X);
	}

}

void AF1ghterFlyCharacter::EndMovementTouch(int32 TouchIndex)
{
	if (TouchIndex != MovementTouchIndex)
	{
		return;
	}
	bIsMoving = false;
	PreviousMoveTouchPosition = FVector2D::ZeroVector;
	MovementTouchIndex = -1;
}

void AF1ghterFlyCharacter::StartDashTouch(FVector2D TouchPosition, int32 TouchIndex)
{
	// Check if it is already dashing
	if ((bIsDashing && DashTouchIndex != -1) || (DashTouchIndex != -1 && DashTouchIndex != TouchIndex))
	{
		return;
	}
	FVector2D ViewportSize;
	if (GEngine && GEngine->GameViewport)
	{
		GEngine->GameViewport->GetViewportSize(ViewportSize);
	}
	else
	{
		ViewportSize = FVector2D(1440.0f, 2960.0f);
	}


	FVector2D NormalizedTouchPosition;
	NormalizedTouchPosition.X = TouchPosition.X / ViewportSize.X;
	NormalizedTouchPosition.Y = TouchPosition.Y / ViewportSize.Y;

	if (NormalizedTouchPosition.X < 0.5f && DashTouchIndex == -1 && TouchIndex != MovementTouchIndex)
	{
		DashInitialTouchPosition = TouchPosition;
		DashTouchIndex = TouchIndex;
		bIsDashing = true;
		return;
	}

	bIsDashing = false;
}

void AF1ghterFlyCharacter::HandleMobileDash(FVector2D TouchPosition, int32 TouchIndex)
{
	// Check if the dash touch is active and if the touch index matches
	if (!bIsDashing || DashTouchIndex == -1 || (DashTouchIndex != -1 && DashTouchIndex != TouchIndex))
	{
		return;
	}

	FVector2D CurrentTouchPosition = TouchPosition;

	// Calculate the swipe delta
	FVector2D SwipeDelta = CurrentTouchPosition - DashInitialTouchPosition;
	float SwipeDistance = SwipeDelta.Size();

	// Check if the swipe distance exceeds the threshold
	if (SwipeDistance >= DashSwipeDistance)
	{
		// Determine the swipe direction (left or right)
		float SwipeDirectionX = SwipeDelta.X / SwipeDistance; // Normalized X component
		if (FMath::Abs(SwipeDirectionX) > 0.5f) // Ensure it's mostly horizontal
		{
			bool bDashLeft = SwipeDirectionX < 0.0f;

			// Trigger the dash
			if (JetCharacterMovementComponent)
			{
				bIsMoving = false; // tempporary disable movement input

				JetCharacterMovementComponent->DashInputPressed(bDashLeft ? DashDirection::DASH_LEFT : DashDirection::DASH_RIGHT);

				// Check if the movement touch is still active, if so, set the movement flag
				if (MovementTouchIndex != -1) {
					bIsMoving = true;
				}

			}

			// Reset the dash touch to prevent multiple dashes from one swipe
			EndDashTouch(TouchIndex);
		}
	}
}

void AF1ghterFlyCharacter::EndDashTouch(int32 TouchIndex)
{
	if (TouchIndex != DashTouchIndex)
	{
		return;
	}
	DashInitialTouchPosition = FVector2D::ZeroVector;
	DashTouchIndex = -1;
	bIsDashing = false;
}

void AF1ghterFlyCharacter::Move(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D MovementVector = Value.Get<FVector2D>();
	/*UE_LOG(LogTemp, Warning, TEXT("Movement x value is: %f"),	MovementVector.X);*/

	if (APawn * ControlledPawn = Controller->GetPawn())
	{
		 //find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();
		const FRotator YawRotation(0, Rotation.Yaw, 0);

		// get forward vector
		const FVector ForwardDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::X);
	
		// get right vector 
		const FVector RightDirection = FRotationMatrix(YawRotation).GetUnitAxis(EAxis::Y);
		
		AddMovementInput(ForwardDirection, MovementVector.Y);
		AddMovementInput(RightDirection, MovementVector.X);
	
	}
	else
	{
		// ue log error
		UE_LOG(LogTemp, Error, TEXT("No pawn controlled by the player"));
	}
}

void AF1ghterFlyCharacter::Look(const FInputActionValue& Value)
{
	// input is a Vector2D
	FVector2D LookAxisVector = Value.Get<FVector2D>();

	if (APawn* ControlledPawn = Controller->GetPawn())
	{
		//find out which way is forward
		const FRotator Rotation = Controller->GetControlRotation();

		const FVector TopDirection = FRotationMatrix(Rotation).GetUnitAxis(EAxis::Z);
		const FVector RightDirection = FRotationMatrix(Rotation).GetUnitAxis(EAxis::Y);


		AddMovementInput(TopDirection, LookAxisVector.Y);
		AddMovementInput(RightDirection, LookAxisVector.X);

		FVector forwardDirection = GetCharacterMovement()->GetForwardVector();
		DrawForwardVector(ControlledPawn, forwardDirection);
		DrawCoordinateSystemForActor(ControlledPawn);
	}
	else
	{
		// ue log error
		UE_LOG(LogTemp, Error, TEXT("No pawn controlled by the player"));
	}
}

void AF1ghterFlyCharacter::Dash(const FInputActionValue& Value)
{
	const float DashingDirection = Value.Get<float>();

	if (JetCharacterMovementComponent) {
		JetCharacterMovementComponent->DashInputPressed(DashingDirection < 0 ? DashDirection::DASH_LEFT : DashDirection::DASH_RIGHT );
	}
}

FCollisionQueryParams AF1ghterFlyCharacter::GetIgnoreCharacterParams() const
{
	FCollisionQueryParams Params;
	TArray<AActor*> CharacterChildren;
	GetAllChildActors(CharacterChildren);
	Params.AddIgnoredActors(CharacterChildren);
	Params.AddIgnoredActor(this);

	return Params;
}

void AF1ghterFlyCharacter::DrawForwardVector(APawn* PlayerPawn, FVector ForwardDir)
{
	if (PlayerPawn)
	{
		// Get the Pawn's location and forward direction
		FVector PawnLocation = PlayerPawn->GetActorLocation();
		FVector ForwardDirection = PlayerPawn->GetActorForwardVector();

		 // If ForwardDir is provided, override the default forward direction
        if (ForwardDir != FVector::ZeroVector) {
            ForwardDirection = ForwardDir;
        }

		// Set the length of the vector you want to draw (e.g., 200 units)
		float LineLength = 100.f;

		// Calculate the end point of the vector based on the forward direction
		FVector LineEnd = PawnLocation + (ForwardDirection * LineLength);

		// Draw a debug line in the world to represent the vector
		// Use DrawDebugLine or DrawDebugArrow depending on your preference
		if (GEngine)
		{
			// Use UKismetSystemLibrary to draw the debug arrow
			UKismetSystemLibrary::DrawDebugArrow(PlayerPawn, PawnLocation, LineEnd, 10.0f, FLinearColor::Red, 0.2f, 2.f);

		}
	}
}

void AF1ghterFlyCharacter::DrawCoordinateSystemForActor(APawn* PlayerPawn)
{
	if (PlayerPawn)
	{
		// Get the actor's location and rotation
		FVector ActorLocation = PlayerPawn->GetActorLocation();
		FRotator ActorRotation = PlayerPawn->GetActorRotation();
		// Add an offset to the Z axis to place the coordinate system a little above the player
		FVector OffsetLocation = ActorLocation + DrawCoordinate;

		// Scale for the axes and the lifetime
		float Scale = 20.f;  // The size of the coordinate axes
		float LifeTime = 0.f; // The duration for how long the coordinate system is visible
		float Thickness = 2.f; // The thickness of the lines

		// Draw the debug coordinate system
		UKismetSystemLibrary::DrawDebugCoordinateSystem(
			PlayerPawn,               // WorldContextObject (can be Actor or GetWorld())
			OffsetLocation,       // AxisLoc (location of the origin of the coordinate system)
			ActorRotation,       // AxisRot (orientation of the coordinate system)
			Scale,               // Scale (size of the coordinate system)
			LifeTime,            // LifeTime (how long to draw it)
			Thickness            // Thickness (line thickness)
		);
	}
}

void AF1ghterFlyCharacter::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
	Super::GetLifetimeReplicatedProps(OutLifetimeProps);

	DOREPLIFETIME_CONDITION(AF1ghterFlyCharacter, Proxy_bDash, COND_SkipOwner);
	DOREPLIFETIME(AF1ghterFlyCharacter, bCanMoveForward);
	DOREPLIFETIME(AF1ghterFlyCharacter, JetForwardSpeed);
}

// Notify the run logic on simulated proxies
void AF1ghterFlyCharacter::OnRep_ProxyDash()
{

	if (JetCharacterMovementComponent) {

		JetCharacterMovementComponent-> DashStartDelegate.Broadcast();
	}
}

// Montage animation
void AF1ghterFlyCharacter::OnDashStart()
{
	// When the dash event is broadcast, play the dash montage.
	if (DashMontage)
	{
		PlayAnimMontage(DashMontage, 1.0f, "Dash");
	}
}

void AF1ghterFlyCharacter::Server_HandleSpawnTriggerEvent_Implementation(const FClientTriggerNextPlatformSpawn& TriggerData)
{
	UE_LOG(LogTemp, Error, TEXT("Received RPC: TriggerPosX = %f, SequenceNo = %d"),
		TriggerData.TriggerPosX, TriggerData.SequenceNo);


	AF1ghterFlyGameMode* F1ghterFlyGameMode = Cast<AF1ghterFlyGameMode>(GetWorld()->GetAuthGameMode());
	if (F1ghterFlyGameMode && Controller)
	{
		F1ghterFlyGameMode->HandlePlatformSpawnRequest({
			Controller,
			TriggerData.SequenceNo,
			TriggerData.TriggerPosX
		});
	}

}
bool AF1ghterFlyCharacter::Server_HandleSpawnTriggerEvent_Validate(const FClientTriggerNextPlatformSpawn& TriggerData)
{
	return true;
}

void AF1ghterFlyCharacter::SubscribeTo_ForwardSpeedChanged()
{

	if (AF1ghterFlyGameState* GameState = GetWorld()->GetGameState<AF1ghterFlyGameState>())
	{
		// If jet is not boosting state, then override jet forward speed
		if (!JetEffectManager->IsJetBoosting()) {
			JetForwardSpeed = GameState->ForwardSpeed; // Assuming OverallSpeedBoost is a float
		}
	}
}

void AF1ghterFlyCharacter::SubscribeTo_OnJetEffectModeChanged(EJetEffectMode Mode)
{
	// If jet boosting is off, make sure jet forward speed is the game state's forward speed
	if (!FCommonUtils::HasThisJetEffectMode(EJetEffectMode::Boosting, Mode)) {
		if (AF1ghterFlyGameState* GameState = GetWorld()->GetGameState<AF1ghterFlyGameState>()) {
			JetForwardSpeed = GameState->ForwardSpeed;
		}
	}

}

#pragma region Damageable logic

void AF1ghterFlyCharacter::DamageTaken(float DamageAmount, float HypeMeterDecayAmount) const
{
	// Handle damage taken
	UE_LOG(LogTemp, Warning, TEXT("Damage Taken: %f"), DamageAmount);

	// If jet is shielding, ignore damage
	if (GetEffectManager()->IsJetShielding()) return;
	
	if (HasAuthority())
	{
		if (AF1ghterFlyPlayerState* F1ghterFlyPlayerState = Cast<AF1ghterFlyPlayerState>(GetPlayerState()))
		{
			F1ghterFlyPlayerState->ReduceHealth(DamageAmount);
			// if damage, decay hype meter
			GetScoreManager()->DecayHypeMeter(HypeMeterDecayAmount);
		}
	}

	if (GetNetMode() == NM_Standalone || GetLocalRole() != ROLE_Authority)
	{
		// Broadcast damage taken event
		GetJetEventManager()->BroadcastDamageTaken(DamageAmount);	
	}

}

#pragma endregion


