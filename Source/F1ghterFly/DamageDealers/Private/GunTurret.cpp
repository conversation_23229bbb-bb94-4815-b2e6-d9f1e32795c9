// Fill out your copyright notice in the Description page of Project Settings.


#include "F1ghterFly/DamageDealers/Public/GunTurret.h"

#include "F1ghterFlyCharacter.h"
#include "ActorComponents/BaseActorEventManager.h"
#include "Components/BoxComponent.h"
#include "F1ghterFly/DamageDealers/Public/TurretProjectile.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"


// Sets default values
AGunTurret::AGunTurret()
{
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

	// set replication
	bReplicates = true;

	// Create Anchor
	USceneComponent* Anchor = CreateDefaultSubobject<USceneComponent>(TEXT("Anchor"));
	RootComponent = Anchor;
	
	// Create a box collider for detection of player jet
	DetectionZone = CreateDefaultSubobject<UBoxComponent>(TEXT("DetectionZone"));
	DetectionZone->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
	DetectionZone->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
	DetectionZone->SetupAttachment(Anchor);

	// Default values
	Health = 100.0f;
	RotationSpeed = 360.0f; // 360 degrees/sec for fast rotation
	MinPitch = -30.0f;
	MaxPitch = 60.0f;
	FireRate = 0.2f; // Fire every 0.2 seconds
	ProjectileSpeed = 5000.0f;
	SleepAfterSeconds = 3.0f;
	PredictionTime = 0.5f; // Predict target position 0.5s ahead
	RandomSpreadDegrees = 2.0f; // Small random spread for projectiles
	bIsJetActive = false;
	LOSCollisionChannel = ECC_Visibility;
}

void AGunTurret::PostInitializeComponents()
{
	Super::PostInitializeComponents();
	
	// setup detection overlap
	DetectionZone->OnComponentBeginOverlap.AddDynamic(this, &AGunTurret::OnOverlapBegin);
	DetectionZone->OnComponentEndOverlap.AddDynamic(this, &AGunTurret::OnOverlapEnd);

	// get reference of turret base and head by tag
	TurretBase = FindComponentByTag<USceneComponent>(TEXT("TurretBase"));
	TurretHead = FindComponentByTag<USceneComponent>(TEXT("TurretHead"));

	// ue log if not found
	if (!TurretBase || !TurretHead)
	{
		UE_LOG(LogTemp, Error, TEXT("TurretBase or TurretHead not found"));
	}

	// find bullet spawn point by tag
	BulletSpawnPoint = FindComponentByTag<USceneComponent>(TEXT("BulletSpawnPoint"));
}

// Called when the game starts or when spawned
void AGunTurret::BeginPlay()
{
	Super::BeginPlay();
	
	// Bind delegates to local methods
	OnStartFire.AddDynamic(this, &AGunTurret::StartFiring);
	OnStopFire.AddDynamic(this, &AGunTurret::StopFiring);
	OnJetDetected.AddDynamic(this, &AGunTurret::DetectJet);
	
}

// Call when destroyed
void AGunTurret::Destroyed()
{
	Super::Destroyed();
	StopFiring();
}

// Called every frame
void AGunTurret::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);
	
	if (bIsJetActive && IsValid(TargetJet))
	{
		// Update target to closest player with LOS
		AF1ghterFlyCharacter* NewTarget = nullptr;
		float ClosestDistance = FVector::Dist(GetActorLocation(), TargetJet->GetActorLocation());

		for (AF1ghterFlyCharacter* Jet : JetsInRange)
		{
			if (!IsValid(Jet) || !CanTargetPlayer(Jet)) continue;

			FVector JetLocation = Jet->GetActorLocation();
			float Distance = FVector::Dist(GetActorLocation(), JetLocation);
			if (Distance <= ClosestDistance && HasLineOfSightToTarget(TurretBase->GetComponentLocation(), JetLocation, Jet))
			{
				ClosestDistance = Distance;
				NewTarget = Jet;
			}
		}

		if (IsValid(NewTarget) && NewTarget != TargetJet)
		{
			TargetJet = NewTarget;
		}

		if (IsValid(TargetJet) && !bIsFiring)
		{
			// TargetJet = NewTarget;
			// OnJetDetected.Broadcast();
			OnStartFire.Broadcast();
		}
		
		// Update turret rotation
		UpdateTurretRotation(DeltaTime);
	}
}

void AGunTurret::DamageTaken(float DamageAmount)
{
	// Any logic to prevent damage from taken, can be added here

	// If an actor has authority, then apply damage
	if (HasAuthority())
	{
		Health -= DamageAmount;

		if (Health <= 0.0f)
		{
			// Destroy();
		}
	}

	// call broadcast event only on clients / standalone
	if (GetNetMode() == NM_Standalone || GetLocalRole() == ROLE_SimulatedProxy)
	{
		// Broadcast damage taken event
		if (UActorComponent* ActorComponent = GetComponentByClass(UBaseActorEventManager::StaticClass()))
		{
			UBaseActorEventManager* BaseActorEventManager = Cast<UBaseActorEventManager>(ActorComponent);
			BaseActorEventManager->BroadcastDamageTaken(DamageAmount);
		}
	}
	
}

// On overlap begin: Jet is detected
void AGunTurret::OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{

	if (!OtherActor || OtherActor == this || !OtherActor->ActorHasTag(FName("Jet"))) return;

	bIsAsleep = false;
	
	AF1ghterFlyCharacter* OtherJet = Cast<AF1ghterFlyCharacter>(OtherActor);
	if (!OtherJet || !CanTargetPlayer(OtherJet)) return;

	JetsInRange.AddUnique(OtherJet);
	if (!bIsJetActive)
	{
		bIsJetActive = true;
		OnJetDetected.Broadcast();
	}
	
}

// On overlap end: Jet is no longer detected
void AGunTurret::OnOverlapEnd(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp,
	int32 OtherBodyIndex)
{
	if (OtherActor == this || !IsValid(OtherActor) || !OtherActor->ActorHasTag(FName("Jet")))
	{
		return;
	}
	
	AF1ghterFlyCharacter* OtherJet = Cast<AF1ghterFlyCharacter>(OtherActor);
	if (OtherJet)
	{
		JetsInRange.Remove(OtherJet);
		if (OtherJet == TargetJet)
		{
			TargetJet = nullptr;
			OnStopFire.Broadcast();
		}
	}

	if (JetsInRange.Num() == 0)
	{
		bIsJetActive = false;
		OnBackToSleep.Broadcast();
	}
}

void AGunTurret::DetectJet()
{
	if (!bIsJetActive) return;

	// Clear Sleep timer
	GetWorld()->GetTimerManager().ClearTimer(BackToSleepTimerHandle);

	bIsAsleep = false;
	
	// Select initial target (closest with LOS)
	AF1ghterFlyCharacter* NewTarget = nullptr;
	float CurrentTargetDistance = IsValid(TargetJet) ? FVector::Dist(GetActorLocation(), TargetJet->GetActorLocation()) : 0;
	
	for (AF1ghterFlyCharacter* Jet : JetsInRange)
	{
		if (!IsValid(Jet) || !CanTargetPlayer(Jet)) continue;

		if (!IsValid(TargetJet))
		{
			NewTarget = Jet;
			break;
		}
		
		FVector JetLocation = Jet->GetActorLocation();
		float Distance = FVector::Dist(GetActorLocation(), JetLocation);
		if (Distance <= CurrentTargetDistance && HasLineOfSightToTarget(TurretBase->GetComponentLocation(), JetLocation, Jet))
		{
			CurrentTargetDistance = Distance;
			NewTarget = Jet;
		}
	}

	if (IsValid(NewTarget))
	{
		TargetJet = NewTarget;
		OnStartFire.Broadcast();
	}
	else
	{
		bIsJetActive = false;
		OnBackToSleep.Broadcast();
	}
}

void AGunTurret::StartFiring()
{
	if (!IsValid(TargetJet)) return;

	bIsFiring = true;
	
	if (!GetWorld()->GetTimerManager().IsTimerActive(FireTimerHandle))
	{
		GetWorld()->GetTimerManager().SetTimer(FireTimerHandle, this, &AGunTurret::FireBullet, FireRate, true);
	}
	
}

void AGunTurret::StopFiring()
{
	GetWorld()->GetTimerManager().ClearTimer(FireTimerHandle);
	GetWorld()->GetTimerManager().SetTimer(BackToSleepTimerHandle, this, &AGunTurret::BackToSleep, SleepAfterSeconds, false);
	bIsFiring = false;
}

void AGunTurret::BackToSleep()
{
	// bIsJetActive = false;
	// TargetJet = nullptr;
	//JetsInRange.Empty();
	bIsFiring = false;
	bIsAsleep = true;
	OnBackToSleep.Broadcast();
}

void AGunTurret::FireBullet()
{
	if (!BulletSpawnPoint || !ProjectileClass || !IsValid(TargetJet)) return;

	FVector SpawnLocation = BulletSpawnPoint->GetComponentLocation();
	FVector TargetLocation = TargetJet->GetActorLocation();

	// Predictive aimin
	float Distance = FVector::Dist(SpawnLocation, TargetLocation);
	float TravelTime = Distance / ProjectileSpeed; // Assume projectile speed
	TargetLocation += TargetJet->GetVelocity() * FMath::Clamp(TravelTime, 0.0f, PredictionTime);
	
	
	FRotator SpawnRotation = UKismetMathLibrary::FindLookAtRotation(SpawnLocation, TargetLocation);

	if (RandomSpreadDegrees > 0.0f)
	{
		SpawnRotation.Pitch += FMath::RandRange(-RandomSpreadDegrees, RandomSpreadDegrees);
		SpawnRotation.Yaw += FMath::RandRange(-RandomSpreadDegrees, RandomSpreadDegrees);
	}

	if (ATurretProjectile* Projectile = GetWorld()->SpawnActor<ATurretProjectile>(ProjectileClass, SpawnLocation, SpawnRotation))
	{
		Projectile->SetDirection(SpawnRotation.Vector(), ProjectileSpeed);
		if (FireSFX)
		{
			UGameplayStatics::PlaySoundAtLocation(this, FireSFX, SpawnLocation, 0.1f);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to spawn projectile for %s"), *GetName());
	}

	DrawDebugLine(GetWorld(), SpawnLocation, SpawnLocation + SpawnRotation.Vector() * 1000.0f, FColor::Red, false, 1.0f);
}

void AGunTurret::UpdateTurretRotation( float DeltaTime)
{

	if (!IsValid(TargetJet) || !TurretBase || !TurretHead) return;

    FVector TargetLocation = TargetJet->GetActorLocation();
    FVector Start = TurretBase->GetComponentLocation();

    if (!HasLineOfSightToTarget(Start, TargetLocation, TargetJet))
    {
        // Try to find another target
        AF1ghterFlyCharacter* NewTarget = nullptr;
        float ClosestDistance = 0;

        for (AF1ghterFlyCharacter* Jet : JetsInRange)
        {
            if (!IsValid(Jet) || Jet == TargetJet || !CanTargetPlayer(Jet)) continue;
            FVector JetLocation = Jet->GetActorLocation();
            float Distance = FVector::Dist(GetActorLocation(), JetLocation);
            if (Distance < ClosestDistance && HasLineOfSightToTarget(Start, JetLocation, Jet))
            {
                ClosestDistance = Distance;
                NewTarget = Jet;
            }
        }

		if (!IsValid(NewTarget))
		{
			 // stop firing
			 OnStopFire.Broadcast();
			 return;
		}
    }

    FRotator LookAtRotation = UKismetMathLibrary::FindLookAtRotation(Start, TargetJet->GetActorLocation());
    DrawDebugLine(GetWorld(), Start, TargetJet->GetActorLocation(), FColor::Green, false, 0.1f);

    // Handle Yaw (TurretBase)
    FRotator CurrentBaseRot = TurretBase->GetComponentRotation();
    FRotator TargetBaseRot = CurrentBaseRot;
	TargetBaseRot.Yaw = LookAtRotation.Yaw;	
    FRotator SmoothedBaseRot = FMath::RInterpTo(CurrentBaseRot, TargetBaseRot, DeltaTime, RotationSpeed);
    TurretBase->SetWorldRotation(SmoothedBaseRot);

    // Handle Pitch (TurretHead)
    FRotator CurrentHeadRot = TurretHead->GetRelativeRotation();
    FRotator TargetHeadRot = FRotator(LookAtRotation.Pitch, 0.0f, 0.0f);
    TargetHeadRot.Pitch = FMath::Clamp(TargetHeadRot.Pitch, MinPitch, MaxPitch);
    FRotator SmoothedHeadRot = FMath::RInterpTo(CurrentHeadRot, TargetHeadRot, DeltaTime, RotationSpeed);
    TurretHead->SetRelativeRotation(SmoothedHeadRot);

}

bool AGunTurret::HasLineOfSightToTarget(const FVector& Start, const FVector& End, AActor* Target) const
{
	if (!IsValid(Target)) return false;

	FHitResult HitResult;
	bool bHit = GetWorld()->LineTraceSingleByChannel(HitResult, Start, End, LOSCollisionChannel, FCollisionQueryParams::DefaultQueryParam);
	return bHit && HitResult.GetActor() == Target;
}

bool AGunTurret::CanTargetPlayer(ACharacter* Player) const
{
	// Empty for future use (e.g., check for invisibility or other effects)
	return true;
}