// Fill out your copyright notice in the Description page of Project Settings.


#include "F1ghterFly/DamageDealers/Public/TurretProjectile.h"

// Sets default values
ATurretProjectile::ATurretProjectile() 
{
	
	// Set this actor to call Tick() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;
	
	// Set replication 
	bReplicates = true;

	// Default values
	MoveSpeed = 5000.0f;
	MaxLifetime = 5.0f;
	CapsuleDamageAmount = 10.0f;
	WingDamageAmount = 5.0f;
	CurrentLifetime = 0.0f;
	
}

// Called when the game starts or when spawned
void ATurretProjectile::BeginPlay()
{
	Super::BeginPlay();
	
}

// Called every frame
void ATurretProjectile::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

	// Update lifetime and destroy if exceeded
	CurrentLifetime += DeltaTime;
	if (CurrentLifetime >= MaxLifetime)
	{
		Destroy();
		return;
	}

	// Move in direction
	if (MoveDirection != FVector::ZeroVector)
	{
		FVector NewLocation = GetActorLocation() + MoveDirection * MoveSpeed * DeltaTime;
		SetActorLocation(NewLocation, true); // Enable sweep for collision
	}
	
}

void ATurretProjectile::OnDamageOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult)
{

	if (!IsValid(OtherActor) || OtherActor == this)
	{
		return;
	}
	
	// broadcast event
	OnProjectileImpact.Broadcast(OtherActor, SweepResult);

	if (OtherActor->ActorHasTag("DamageTaker"))
	{
		// If projectile hit a jet
		if (OtherActor->ActorHasTag("Jet"))
		{
			
			if (AF1ghterFlyCharacter* Jet = Cast<AF1ghterFlyCharacter>(OtherActor))
			{
			
				bool bIsWingHit = IsValid(OtherComp) && OtherComp->ComponentHasTag(FName("Wing"));
				EJetBodyPart BodyPart = bIsWingHit ? EJetBodyPart::Wings : EJetBodyPart::MainBody;

				// server/ standalone will apply damage
				float Damage = GetDamageAmount(BodyPart);
				ApplyDamage(Jet, BodyPart, Damage);
			
			}
		}
		// if not player, then some other ideas....
		else
		{
			ApplyDamage(OtherActor, EJetBodyPart::None, CapsuleDamageAmount);
		}
	}
	
}


void ATurretProjectile::SetDirection(const FVector& Direction, const float ProjectileSpeed)
{
	if (ProjectileSpeed > 0.0f)
	{
		MoveSpeed = ProjectileSpeed;
	}

	// Set normalized direction
	MoveDirection = Direction.GetSafeNormal();
}
