// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Interfaces/IDamageable.h"
#include "GunTurret.generated.h"


// Add delegates for start fire, stop fire, jet detection, back to sleep
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnStartFireDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnStopFireDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnJetDetectedDelegate);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnBackToSleepDelegate);


class AF1ghterFlyCharacter;
class UBoxComponent;
class USceneComponent;
// enum



UCLASS()
class F1GHTERFLY_API AGunTurret : public AActor, public IDamageable
{
	GENERATED_BODY()

public:
	// Sets default values for this actor's properties
	AGunTurret();
	
	// Called every frame
	virtual void Tick(float DeltaTime) override;

	// Post Initialize
	virtual void PostInitializeComponents() override;

	virtual void Destroyed() override;
	
protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Detection")
	UBoxComponent* DetectionZone;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* TurretBase;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* TurretHead;

	// spawn point for bullets
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* BulletSpawnPoint;

	// IDamageable Interface
public:
	virtual void DamageTaken(float DamageAmount) override;

	UPROPERTY(Replicated, EditAnywhere, Category = "Health")
	float Health = 100.f;
	
public:
	
	// Delegate for start fire
	UPROPERTY(BlueprintAssignable, Category = "Fire")
	FOnStartFireDelegate OnStartFire;
	// Delegate for stop fire
	UPROPERTY(BlueprintAssignable, Category = "Fire")
	FOnStopFireDelegate OnStopFire;
	// Delegate for jet detected
	UPROPERTY(BlueprintAssignable, Category = "Detection")
	FOnJetDetectedDelegate OnJetDetected;
	// Delegate for back to sleep
	UPROPERTY(BlueprintAssignable, Category = "Detection")
	FOnBackToSleepDelegate OnBackToSleep;

	// Jet reference that will receive fire
    UPROPERTY(BlueprintReadOnly, Category = "Detection")
	TObjectPtr<AF1ghterFlyCharacter> TargetJet;
	
	UPROPERTY()
	TArray<AF1ghterFlyCharacter*> JetsInRange;
	
	// Is jet active or asleep
	UPROPERTY(BlueprintReadOnly, Category = "Detection")
	bool bIsJetActive = false;

	UPROPERTY(BlueprintReadOnly, Category = "Turret")
	bool bIsFiring = false;

	UPROPERTY(BlueprintReadOnly, Category = "Turret")
	bool bIsAsleep = true;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret")
	TEnumAsByte<ECollisionChannel> LOSCollisionChannel;

	UPROPERTY(EditDefaultsOnly, Category = "Turret")
	float FireRate = 0.5f;

	UPROPERTY(EditAnywhere, Category = "Turret")
	float ProjectileSpeed = 5000.f;

	UPROPERTY(EditAnywhere, Category = "Turret")
	float RotationSpeed = 5.f;

	UPROPERTY(EditAnywhere, Category = "Aiming")
	float DetectionIntervalRate = 0.04f;
	
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret")
	float PredictionTime; // Seconds to predict target position

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Turret")
	float RandomSpreadDegrees; // Random spread for projectile direction

	// Max and min pitch
	UPROPERTY(EditAnywhere, Category = "Turret")
	float MaxPitch = 50.f;

	UPROPERTY(EditAnywhere, Category = "Turret")
	float MinPitch = -50.f;

	// Goes to sleep after ... seconds
	UPROPERTY(EditAnywhere, Category = "Turret")
	float SleepAfterSeconds = 2.f;

	// Projectile class
	UPROPERTY(EditAnywhere, Category = "Firing")
	TSubclassOf<class ATurretProjectile> ProjectileClass;

	// Fire SFX
	UPROPERTY(EditAnywhere, Category = "SFX")
	USoundBase* FireSFX;
	
private:
	// on being overlap
	UFUNCTION()
	void OnOverlapBegin(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);
	// on end overlap
	UFUNCTION()
	void OnOverlapEnd(UPrimitiveComponent* OverlappedComp, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex);

	// Update Turret Rotation
	UFUNCTION()
	void UpdateTurretRotation( float DeltaTime );
	// Back to sleep
	UFUNCTION()
	void BackToSleep();


	
private:
	FTimerHandle FireTimerHandle;
	FTimerHandle BackToSleepTimerHandle;
	
	UFUNCTION()
	void DetectJet();
	
	UFUNCTION(BlueprintCallable, Category = "Fire")
	void StartFiring();

	UFUNCTION()
	void StopFiring();

	UFUNCTION()
	void FireBullet();
	
public:
	UFUNCTION(BlueprintCallable, Category = "Turret")
	bool HasLineOfSightToTarget(const FVector& Start, const FVector& End, AActor* Target) const;

	UFUNCTION( BlueprintCallable, Category = "Turret")
	bool CanTargetPlayer(ACharacter* Player) const;
};
