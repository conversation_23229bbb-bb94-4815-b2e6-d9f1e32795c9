// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "PlatformTypes.h"
#include <ScoringTypes.h>
#include "F1ghterFlyPlayerState.generated.h"

DECLARE_MULTICAST_DELEGATE_OneParam(FOnScoreUpdated, float);
DECLARE_DYNAMIC_MULTICAST_DELEGATE(FOnHypeMeterUpdated);


/**
 * 
 */
UCLASS()
class F1GHTERFLY_API AF1ghterFlyPlayerState : public APlayerState
{
	GENERATED_BODY()

public:
	AF1ghterFlyPlayerState();

	virtual void BeginPlay() override;

	virtual void BeginDestroy() override;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

public:
	// Health
	UPROPERTY(ReplicatedUsing = OnRep_Health, BlueprintReadOnly, Category = "Health")
	float Health;

	UPROPERTY()
	bool bIsGameOver;

protected:
	UPROPERTY(ReplicatedUsing = OnRep_CurrentScore)
	float CurrentScore;

	UPROPERTY(ReplicatedUsing = OnRep_HypeMeterProgress)
	float HypeMeterProgress;

	UPROPERTY(ReplicatedUsing = OnRep_HypeRank, BlueprintReadOnly, Category = "Score")
	EHypeRank HypeRank;

	// Active score multiplier (from combos, effect modes, etc.)
	UPROPERTY(Replicated, BlueprintReadOnly, Category = "Score")
	float ScoreMultiplier = 0.0f;


public:
	UFUNCTION()
	void OnGameOver();

	UFUNCTION()
	void OnRep_Health();

	UFUNCTION(BlueprintCallable, Category = "Health")
	float GetHealth() const { return Health; }

	UFUNCTION(BlueprintCallable, Category = "Health")
	float GetMaxHealth() const { return MaxHealth; }

	// Server-side function to reduce health
	UFUNCTION()
	void ReduceHealth(float Damage);

	// Enhanced damage application with body part and hype meter decay
	UFUNCTION()
	void ApplyDamage(float DamageAmount, EJetBodyPart BodyPart, float HypeMeterDecayAmount);

	UFUNCTION()
	void IncreaseHealth(int16 AddedHealth = 0);

	// Sync visual state when jet becomes relevant (handles low health effects, etc.)
	UFUNCTION()
	void SyncJetVisualState();


	UFUNCTION()
	void OnRep_CurrentScore();

	UFUNCTION()
	void OnRep_HypeMeterProgress();

	UFUNCTION()
	void OnRep_HypeRank();

	UFUNCTION(BlueprintCallable, Category = "Score")
	float GetCurrentScore() const { return CurrentScore; }
	
	UFUNCTION(BlueprintCallable, Category = "Score")
	float GetHypeMeterProgress() const { return HypeMeterProgress; }
	
	UFUNCTION(BlueprintCallable, Category = "Score")
	EHypeRank GetHypeRank() const { return HypeRank; }

	UFUNCTION(BlueprintCallable, Category = "Score")
	void SetHypeRank(EHypeRank NewRank)
	{
		if (HasAuthority())
		{
			HypeRank = NewRank;
		}
	}

	UFUNCTION(BlueprintCallable, Category = "Score")

	void SetCurrentScore(float NewScore)
	{
		if (HasAuthority())
		{
			CurrentScore = NewScore;
		}

		// Call onRep function to perform logic such as UI updates when score changes. Standalone ONLY
		if (GetNetMode() == NM_Standalone) {
			OnRep_CurrentScore();
		}
	}
	
	UFUNCTION(BlueprintCallable, Category = "Score")
	void DecayHypeMeter(float DecayAmount)
	{
		SetHypeMeterProgress(FMath::Max(0.0f, GetHypeMeterProgress() - DecayAmount));
	}
	
	UFUNCTION(BlueprintCallable, Category = "Score")
	void SetHypeMeterProgress(float NewProgress)
	{
		
		HypeMeterProgress = NewProgress;
		bool WasRankS = IsRankSActive();

		// Update hype rank
		if (HypeMeterProgress >= RankThresholds[EHypeRank::RankS])
		{
			HypeRank = EHypeRank::RankS;
		}
		else if (HypeMeterProgress >= RankThresholds[EHypeRank::RankA])
		{
			HypeRank = EHypeRank::RankA;
		}
		else if (HypeMeterProgress >= RankThresholds[EHypeRank::RankB])
		{
			HypeRank = EHypeRank::RankB;
		}
		else
		{
			HypeRank = EHypeRank::RankC;
		}

		if (WasRankS && HypeRank != EHypeRank::RankS)
		{
			HypeMeterProgress -= RankThresholds[EHypeRank::RankS] * 0.5; // Demotion to previous rank will reduce the hype meter values
		}

		// Call onRep function to perform logic such as UI updates when score changes. Standalone ONLY
		if (GetNetMode() == NM_Standalone) {
			OnRep_HypeMeterProgress();
		}
	}

	UFUNCTION(BlueprintCallable, Category = "Score")
	bool IsRankSActive() const
	{
		return HypeRank == EHypeRank::RankS;
	}

	UFUNCTION(BlueprintCallable, Category = "Score")
	float GetScoreMultiplier() const { return FMath::Max(ScoreMultiplier, 1.0f); }

	UFUNCTION(BlueprintCallable, Category = "Score")
	void SetScoreMultiplier(float NewMultiplier)
	{
		if (HasAuthority())
		{
			ScoreMultiplier += NewMultiplier;

			// Ensure multiplier does not go below 0.0
			ScoreMultiplier = FMath::Max(0.0f, ScoreMultiplier);
		}
	}
protected:
	UPROPERTY()
	float MaxHealth;

//Delegates
public:

	FOnScoreUpdated OnScoreUpdated;

	UPROPERTY(BlueprintAssignable, Category = "Score")
	FOnHypeMeterUpdated OnHypeMeterUpdated;
};
