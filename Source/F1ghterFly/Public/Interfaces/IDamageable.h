#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "IDamageable.generated.h"

UINTERFACE(MinimalAPI)
class UDamageable : public UInterface
{
	GENERATED_BODY()
};

class F1GHTERFLY_API IDamageable
{
	GENERATED_BODY()

public:

	// Health of the actor
	float Health = 100.0f;
	
	bool onFire = false;
	
	// Base damage (for enemies, props, etc.)
	virtual void DamageTaken(float DamageAmount) = 0;
};
