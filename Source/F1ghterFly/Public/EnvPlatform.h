// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "F1ghterFly/Types/PlatformTypes.h"
#include "Components/BoxComponent.h"
#include <Components/SplineComponent.h>
#include "EnvPlatform.generated.h"

class UBoxComponent;
class ASpawnManager;

UCLASS()
class F1GHTERFLY_API AEnvPlatform : public AActor
{
	GENERATED_BODY()
	
public:	
	// Sets default values for this actor's properties
	AEnvPlatform();

protected:
	// Called when the game starts or when spawned
	virtual void BeginPlay() override;
	virtual void PostInitializeComponents() override;
	virtual bool IsNetRelevantFor(const AActor* RealViewer, const AActor* ViewTarget, const FVector& SrcLocation) const;

	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
	
protected:
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly)
	class UStaticMeshComponent* BaseMesh;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Triggers")
	UBoxComponent* SpawnNextPlatformTrigger;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Triggers")
	UBoxComponent* SpawnLocation;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Triggers")
	UBoxComponent* ReturnToPoolTrigger;

	// Max distance from the jet for the platform to be relevant for replication
	UPROPERTY(EditDefaultsOnly, Category = "Replication")
	float MaxRelevantDistanceForReplication = 70000.0f;

	UPROPERTY()
	TArray<USplineComponent*> ShardTrailSplines;

	UPROPERTY(Replicated)
	int16 SequenceNumber;
	
	UPROPERTY(Replicated, BlueprintReadWrite, EditAnywhere, Category = "Platform Traits")
	EPlatformType PlatformType;

	UPROPERTY(Replicated, BlueprintReadWrite, EditAnywhere, Category = "Platform Traits")
	int32 ObstacleVariation;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Platform", meta = (AllowPrivateAccess = "true"))
	USceneComponent* PowerUpSpawnRoot;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Platform", meta = (AllowPrivateAccess = "true"))
	USceneComponent* ShardTrailSplineRoot;

	UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Platform Traits")
	TArray<USceneComponent*> PowerUpSpawners;

	// Function to update the text
	UFUNCTION(BlueprintCallable, Category = "Platform Info UI")
	FString GetSequenceNoText() const { return FString::Printf(TEXT("Sequence No: %d"), SequenceNumber); }


public:
	FORCEINLINE UBoxComponent* GetSpawnTrigger()  const { return SpawnNextPlatformTrigger; };
	FORCEINLINE UBoxComponent* GetSpawnLocationBox() const { return SpawnLocation; };
	FORCEINLINE FVector GetSpawnLocation() const { return InitialSpawnLocation; };
	FORCEINLINE EPlatformType GetPlatformType() const { return PlatformType; };
	FORCEINLINE int16 GetObstacleVariation() const { return ObstacleVariation; };
	FORCEINLINE int16 GetSequenceNumber() const { return SequenceNumber; };
	FORCEINLINE const TArray<USceneComponent*>& GetPowerUpSpawners() const { return PowerUpSpawners; }
	FORCEINLINE const TArray<USplineComponent*>& GetShardTrailSplines() const { return ShardTrailSplines; }


	void PreparePlatform(FPlatformTraits Traits, int16 SequenceNo);
	void DisableActorAndComponentCollisions();
	void EnableActorAndComponentCollisions();
	void DisablePlatform();
	void EnablePlatform(bool skipCollisionActivation = false);
	void SetPlatformLocation(FVector Location);
	void ShouldActivateSpawnTrigger(bool isActive);

	UPROPERTY()
	bool HasActivePowerUps;

protected:

	// Overlap event handler
	UFUNCTION()
	void OnNextSpawnTriggerOverlap(class UPrimitiveComponent* OverlappedComponent, class AActor* OtherActor,
		class UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep,
		const FHitResult& SweepResult);

	UFUNCTION()
	void OnReturnToPoolTriggerOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

	UFUNCTION()
	void RetrieveShardTrailSplinesComponents();

	UFUNCTION()
	void RetrievePowerUpSpawnersCompenents();
private:
	UPROPERTY()
	FVector InitialSpawnLocation;

	UFUNCTION()
	void OnActivatePlatform(int32 TriggeredSequenceNumber);
};