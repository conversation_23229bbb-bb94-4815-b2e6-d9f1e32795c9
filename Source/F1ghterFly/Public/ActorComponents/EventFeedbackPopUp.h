// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Components/ActorComponent.h"
#include "EventFeedbackPopUp.generated.h"

class UBaseActorEventManager;
class AF1ghterFlyPlayerController;

UCLASS(ClassGroup=(Custom), meta=(BlueprintSpawnableComponent), Blueprintable)
class F1GHTERFLY_API UEventFeedbackPopUp : public UActorComponent
{
	GENERATED_BODY()

public:
	// Sets default values for this component's properties
	UEventFeedbackPopUp();

protected:
	// Called when the game starts
	virtual void BeginPlay() override;
	
public:
	
	// The event manager that will trigger the feedbacks
	UPROPERTY(BlueprintReadOnly, Category = "Feedback")
	UBaseActorEventManager* EventManager;

	// Feedback widget scene component that will be used to position the feedback widget
	UPROPERTY(BlueprintReadOnly, Category = "Feedback")
	USceneComponent* FeedbackWidgetSceneComponent;

public:
	UFUNCTION(BlueprintCallable, Category = "Feedback")
	FVector2D GetFeedbackWidgetScreenPosition();

	UFUNCTION(BlueprintCallable, Category = "Jet")
	APlayerController* GetPlayerController() const;
	

};
