// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Character.h"
#include "Logging/LogMacros.h"
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "F1ghterFly/Types/PlatformTypes.h"
#include "NiagaraComponent.h"
#include "F1ghterFlyCharacter.generated.h"

class USpringArmComponent;
class UCameraComponent;
class UInputMappingContext;
class UInputAction;
struct FInputActionValue;
class UBoxComponent;

DECLARE_LOG_CATEGORY_EXTERN(LogTemplateCharacter, Log, All);

// Delegate for the JetImpact event
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnJetImpactDelegate, EJetBodyPart, BodyPart);

UCLASS(config=Game)
class F1GHTERFLY_API AF1ghterFlyCharacter : public ACharacter
{
	GENERATED_BODY()

public:
	AF1<PERSON><PERSON><PERSON><PERSON><PERSON>cter(const FObjectInitializer& ObjectInitializer);

protected:
	virtual void NotifyControllerChanged() override;

	virtual void SetupPlayerInputComponent(class UInputComponent* PlayerInputComponent) override;

	virtual void BeginPlay() override;
	virtual void BeginDestroy() override;
	virtual void PostInitializeComponents() override;
	virtual void Tick(float DeltaTime) override;

	virtual void OnRep_PlayerState() override;

private:
	UPROPERTY(EditInstanceOnly) class UJetCharacterMovementComponent* JetCharacterMovementComponent;
	UPROPERTY(EditInstanceOnly) class UEffectManager* JetEffectManager;
	UPROPERTY(EditInstanceOnly) class UScoreManager* ScoreManager;
public:
	UPROPERTY(EditInstanceOnly, BlueprintReadOnly) class UJetEventManager* JetEventManager;
	
	UPROPERTY(VisibleAnywhere, Category = "VFX")
	TArray<UNiagaraComponent*> VFXComponents; // currently only being use for powerup effects

	// Low damage vfx component
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "VFX")
	UNiagaraComponent* LowHealthVFXComponent;
	
	// Wing colliders
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Collision")
	UBoxComponent* LeftWingCollider;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Collision")
	UBoxComponent* RightWingCollider;

	// The collider that will be used to detect the collectible items
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Collision")
	UBoxComponent* CollectibleCollider;

	/** Camera boom positioning the camera behind the character */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera, meta = (AllowPrivateAccess = "true"))
	USpringArmComponent* CameraBoom;

	/** Follow camera */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera, meta = (AllowPrivateAccess = "true"))
	UCameraComponent* FollowCamera;

	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = Camera)
	UCameraComponent* TopDownCamera;
	
	/** MappingContext */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputMappingContext* DefaultMappingContext;

	/** Move Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MoveAction;

	/** Look Input Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* LookAction;

	/** Dash Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* DashAction;

	/** Mobile Touch Move Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MobileMoveAction;

	/** Mobile Touch Move Action */
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = Input, meta = (AllowPrivateAccess = "true"))
	UInputAction* MobileDashAction;

	// Pitch variables
	UPROPERTY(EditAnywhere, Category = "Jet Movement")
	float PitchSpeed = 2.0f;

	UPROPERTY(EditAnywhere, Category = "Jet Movement")
	float MinPitch = -45.0f;

	UPROPERTY(EditAnywhere, Category = "Jet Movement")
	float MaxPitch = 45.0f;

	// Sounds.  Will probably move somewhere else when we implement the sounds system
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* ShardCollectSound;

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
	USoundBase* PowerUpCollectSound;

	UPROPERTY()
	UAudioComponent* CollectionAudioComponent;

	// Scene component to define the widget's position
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Feedback")
	USceneComponent* FeedbackWidgetSceneComponent;

protected:
	
	// Touch index for movement
	int16 MovementTouchIndex;

	// Touch index for dash
	int16 DashTouchIndex;

	// Flag to track if a move touch is active
	bool bIsMoving;

	// Flag to track if a dash touch is active
	bool bIsDashing;

	// Initial touch position for dash swipe (in pixel coordinates)
	FVector2D DashInitialTouchPosition;

	// Previous touch position (in pixel coordinates) - for frame-to-frame delta
	FVector2D PreviousMoveTouchPosition;

	FTimerHandle FeedbackPopupTimerHandle;

protected:
	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void HandleMobileMove(FVector2D TouchPosition, int32 TouchIndex);
	
	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void HandleMobileDash(FVector2D TouchPosition, int32 TouchIndex);

	// Handle the start of a touch (record initial position)
	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void StartMovementTouch(FVector2D TouchPosition, int32 TouchIndex);

	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void StartDashTouch(FVector2D TouchPosition, int32 TouchIndex);
	
	// Handle the end of a touch (reset state)
	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void EndMovementTouch(int32 TouchIndex);

	UFUNCTION(BlueprintCallable, Category = "Jet Movement Touch")
	void EndDashTouch(int32 TouchIndex);

	// Sensitivity for touch movement
	UPROPERTY(EditAnywhere, Category = "Mobile Touch")
	float TouchSensitivity;

	// Smoothing factor for touch input (0 = no smoothing, 1 = heavy smoothing)
	UPROPERTY(EditAnywhere, Category = "Mobile Touch")
	float TouchSmoothingFactor;

	// Dead zone for touch input (in normalized units, after sensitivity)
	UPROPERTY(EditAnywhere, Category = "Mobile Touch")
	float TouchDeadZone;

	// Minimum swipe distance to trigger a dash (in pixels)
	UPROPERTY(EditAnywhere, Category = "Dash")
	float DashSwipeDistance;


	/** Called for movement input */
	void Move(const FInputActionValue& Value);

	/** Called for looking input */
	void Look(const FInputActionValue& Value);
	
	void Dash(const FInputActionValue& Value);

	void SwitchCamera();

	// Sync visual state when jet becomes relevant (handles low health effects, etc.)
	void SyncJetVisualState();

public:
	// Montages
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Animation")
	UAnimMontage* DashMontage;

	FCollisionQueryParams GetIgnoreCharacterParams() const;

public:
	/** Returns CameraBoom subobject **/
	FORCEINLINE class USpringArmComponent* GetCameraBoom() const { return CameraBoom; }
	/** Returns FollowCamera subobject **/
	FORCEINLINE class UCameraComponent* GetFollowCamera() const { return FollowCamera; }
	/* Return Jet Character Movement Component*/
	FORCEINLINE class UJetCharacterMovementComponent* GetJetCharacterMovementComponent() const { return JetCharacterMovementComponent; }
	/** Returns Score Manager Component **/
	FORCEINLINE class UScoreManager* GetScoreManager() const { return ScoreManager; }

	FORCEINLINE class UEffectManager* GetEffectManager() const { return JetEffectManager; }

	FORCEINLINE class UJetEventManager* GetJetEventManager() const {return JetEventManager;}


	/**
	* Draws a forward direction vector for the given pawn in the world.
	* @param PlayerPawn The pawn to get the forward vector for.
	*/
	UFUNCTION(BlueprintCallable, Category = "Utilities")
	void DrawForwardVector(APawn* PlayerPawn, FVector ForwardDir);

	void DrawCoordinateSystemForActor(APawn* PlayerPawn);

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Draw Debug") FVector DrawCoordinate;

	UFUNCTION()
	void SetForwardSpeed(float Speed) {
		JetForwardSpeed = Speed;
	};

// Replication
public:
	 // Proxy Dash replication. Can be use to replicate dash animation of a simulated proxy, among others logic
	UPROPERTY(ReplicatedUsing = OnRep_ProxyDash) bool Proxy_bDash;

	UPROPERTY(Replicated)
	bool bCanMoveForward; // Persistent, replicated stop state

	UPROPERTY(Replicated)
	float JetForwardSpeed = 0.0f;

public:
	UFUNCTION() void OnRep_ProxyDash();
	// Event handler for the dash start
	UFUNCTION() void OnDashStart();

public:
	virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;

//RPC
public:

	// Client -> Server RPC to send trigger event for next platform generation
	UFUNCTION(Server, Reliable, WithValidation)
	void Server_HandleSpawnTriggerEvent(const FClientTriggerNextPlatformSpawn& TriggerData);

	void Server_HandleSpawnTriggerEvent_Implementation(const FClientTriggerNextPlatformSpawn& TriggerData);
	bool Server_HandleSpawnTriggerEvent_Validate(const FClientTriggerNextPlatformSpawn& TriggerData);

//Delegates
public:
	// Delegate to broadcast when the jet is impacted
	UPROPERTY(BlueprintAssignable, Category = "Damage")
	FOnJetImpactDelegate OnJetImpact;

	UFUNCTION()
	void SubscribeTo_ForwardSpeedChanged();

	// Subcription to Jet Effect mode changes
	UFUNCTION()
	void SubscribeTo_OnJetEffectModeChanged(EJetEffectMode Mode);


};

