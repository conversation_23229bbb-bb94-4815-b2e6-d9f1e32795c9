// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include <PlatformTypes.h>
#include <GameplayWidget.h>
#include "F1ghterFlyPlayerController.generated.h"

/**
 * 
 */
UCLASS()
class F1GHTERFLY_API AF1ghterFlyPlayerController : public APlayerController
{
	GENERATED_BODY()
	
public:
	AF1ghterFlyPlayerController();

	virtual void BeginPlay() override;
	virtual void ReceivedPlayer() override;


protected:


	UFUNCTION(BlueprintCallable, Category = "Damage")
	void OnJetImpact(EJetBodyPart BodyPart);

	UPROPERTY()
	UGameplayWidget* GameplayWidget;

	UPROPERTY(EditAnywhere, Category="Widget")
	TSubclassOf<UGameplayWidget> GameplayWidgetClass;

	UPROPERTY(EditAnywhere, Category = "Camera")
	TSubclassOf<UCameraShakeBase> MainBodyDamageShake;

	UPROPERTY(EditAnywhere, Category = "Camera")
	TSubclassOf<UCameraShakeBase> WingDamageShake;

public:

	/** RPC to restart game */
	UFUNCTION(Server, Reliable, BlueprintCallable)
	void Server_RestartGame();
	
	FORCEINLINE UGameplayWidget* GetGameplayWidget() const { return GameplayWidget; };

	void CreateGameWidget();
};
