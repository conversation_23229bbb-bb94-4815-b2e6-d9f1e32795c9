#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/BoxComponent.h"
#include "F1ghterFlyCharacter.h"
#include "F1ghterFlyPlayerState.h"
#include "Projectile.h"
#include "DamageDealer.generated.h"

UCLASS(Abstract)
class F1GHTERFLY_API ADamageDealer : public AActor
{
    GENERATED_BODY()

public:
    ADamageDealer();

    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;
    virtual void PostInitializeComponents() override;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float WingDamageAmount;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float CapsuleDamageAmount = 10.0f;

    // Cooldown between damage applications (to prevent rapid damage)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float DamageCooldown = 1.5f;

    // Amount of decay to hype meter per hit. If wing hit, the decay amount will be half of this amount
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    float HypeMeterDecayAmount = 200.0f;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Damage")
    EDamageResidualEffectType DamageResidualEffectType;

    UFUNCTION()
    void SetDamageOverlapEnabled(bool bEnabled);

protected:
    // Colliders for detecting overlaps (e.g., with jet wings)
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Collision")
    TArray<UBoxComponent*> DamageColliders;

    // Called when the damage collider overlaps with another actor
    // Called when the damage collider overlaps with another actor
    UFUNCTION()
    virtual void OnDamageOverlap(UPrimitiveComponent* OverlappedComponent, AActor* OtherActor, UPrimitiveComponent* OtherComp, int32 OtherBodyIndex, bool bFromSweep, const FHitResult& SweepResult);

    // Apply damage to the target actor (e.g., the jet)
    UFUNCTION(BlueprintCallable, Category = "Damage")
    virtual void ApplyDamage(AActor* TargetActor, const EJetBodyPart Part, const float Damage);

    // Can this object apply damage to the target? (e.g., checks cooldown)
    UFUNCTION(BlueprintCallable, Category = "Damage")
    virtual bool CanApplyDamage(AActor* TargetActor) const;

    // Get the damage amount for the specified body part
    UFUNCTION(BlueprintCallable, Category = "Damage")
    virtual float GetDamageAmount(EJetBodyPart Part) const;
    
    UFUNCTION()
	void StartNearMissTimer(AF1ghterFlyCharacter* Jet, float Proximity);

    // Process a pending near miss after delay
    UFUNCTION()
    void ProcessPendingNearMiss();

    // Cached references for pending near miss
    UPROPERTY()
    AF1ghterFlyCharacter* PendingNearMissJet;

    // Proximity of the pending near miss
    UPROPERTY()
    float PendingNearMissProximity;
    

private:
    UFUNCTION()
    void InitializeDamageColliders();
    
    // Track the last time damage was applied to each actor
    TMap<AActor*, float> LastDamageTimes;

    // Timer handle for pending near miss
    FTimerHandle NearMissTimerHandle;
};